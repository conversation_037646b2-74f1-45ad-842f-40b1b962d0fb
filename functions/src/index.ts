import { onCall, HttpsError } from "firebase-functions/v2/https";
import { onDocumentCreated } from "firebase-functions/v2/firestore";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp } from "firebase-admin/app";

// Initialize Firebase Admin
initializeApp();

const auth = getAuth();
const db = getFirestore();

interface UserData {
  uid: string;
  email: string;
  displayName?: string;
  role: 'admin' | 'user';
  createdAt: string;
  lastLoginAt?: string;
}

// Set user role (admin only)
export const setUserRole = onCall(async (request) => {
  // Check if user is authenticated
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if user is admin
  const callerRecord = await auth.getUser(request.auth.uid);
  const callerClaims = callerRecord.customClaims;

  if (!callerClaims?.role || callerClaims.role !== 'admin') {
    throw new HttpsError('permission-denied', 'Only admins can set user roles');
  }

  const { uid, role } = request.data;

  if (!uid || !role) {
    throw new HttpsError('invalid-argument', 'uid and role are required');
  }

  if (!['admin', 'user'].includes(role)) {
    throw new HttpsError('invalid-argument', 'role must be either "admin" or "user"');
  }

  try {
    // Set custom claims
    await auth.setCustomUserClaims(uid, { role });

    // Update user document in Firestore
    await db.collection('users').doc(uid).update({
      role,
      updatedAt: new Date().toISOString(),
      updatedBy: request.auth.uid
    });

    return { success: true };
  } catch (error) {
    console.error('Error setting user role:', error);
    throw new HttpsError('internal', 'Failed to set user role');
  }
});

// Get all users (admin only)
export const getAllUsers = onCall(async (request) => {
  // Check if user is authenticated
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if user is admin
  const callerRecord = await auth.getUser(request.auth.uid);
  const callerClaims = callerRecord.customClaims;

  if (!callerClaims?.role || callerClaims.role !== 'admin') {
    throw new HttpsError('permission-denied', 'Only admins can view all users');
  }

  try {
    const listUsersResult = await auth.listUsers();
    const users: UserData[] = [];

    for (const userRecord of listUsersResult.users) {
      const role = userRecord.customClaims?.role || 'user';

      users.push({
        uid: userRecord.uid,
        email: userRecord.email || '',
        displayName: userRecord.displayName,
        role: role as 'admin' | 'user',
        createdAt: userRecord.metadata.creationTime,
        lastLoginAt: userRecord.metadata.lastSignInTime
      });
    }

    return { users };
  } catch (error) {
    console.error('Error getting users:', error);
    throw new HttpsError('internal', 'Failed to get users');
  }
});

// Create user document when user signs up
export const createUserDocument = onDocumentCreated("users/{userId}", async (event) => {
  const userId = event.params?.userId;

  if (!userId) {
    console.error('No userId provided');
    return;
  }

  try {
    const userRecord = await auth.getUser(userId);

    // Set default role as 'user'
    await auth.setCustomUserClaims(userId, { role: 'user' });

    // Create user document
    const userData = {
      uid: userId,
      email: userRecord.email,
      displayName: userRecord.displayName || null,
      role: 'user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await event.data?.ref.set(userData, { merge: true });

    console.log(`User document created for ${userId}`);
  } catch (error) {
    console.error('Error creating user document:', error);
  }
});

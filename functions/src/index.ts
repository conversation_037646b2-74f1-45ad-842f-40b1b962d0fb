import * as functions from "firebase-functions";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp } from "firebase-admin/app";

// Initialize Firebase Admin
initializeApp();

const auth = getAuth();
const db = getFirestore();

interface UserData {
  uid: string;
  email: string;
  displayName?: string;
  role: 'admin' | 'user';
  createdAt: string;
  lastLoginAt?: string;
}

// Set user role (admin only)
export const setUserRole = functions.https.onCall(async (data: any, context: any) => {
  // Check if user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if user is admin
  const callerRecord = await auth.getUser(context.auth.uid);
  const callerClaims = callerRecord.customClaims;

  if (!callerClaims?.role || callerClaims.role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can set user roles');
  }

  const { uid, role } = data;

  if (!uid || !role) {
    throw new functions.https.HttpsError('invalid-argument', 'uid and role are required');
  }

  if (!['admin', 'user'].includes(role)) {
    throw new functions.https.HttpsError('invalid-argument', 'role must be either "admin" or "user"');
  }

  try {
    // Set custom claims
    await auth.setCustomUserClaims(uid, { role });

    // Update user document in Firestore
    await db.collection('users').doc(uid).update({
      role,
      updatedAt: new Date().toISOString(),
      updatedBy: context.auth.uid
    });

    return { success: true };
  } catch (error) {
    console.error('Error setting user role:', error);
    throw new functions.https.HttpsError('internal', 'Failed to set user role');
  }
});

// Get all users (admin only)
export const getAllUsers = functions.https.onCall(async (data: any, context: any) => {
  // Check if user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if user is admin
  const callerRecord = await auth.getUser(context.auth.uid);
  const callerClaims = callerRecord.customClaims;

  if (!callerClaims?.role || callerClaims.role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can view all users');
  }

  try {
    const listUsersResult = await auth.listUsers();
    const users: UserData[] = [];

    for (const userRecord of listUsersResult.users) {
      const role = userRecord.customClaims?.role || 'user';

      users.push({
        uid: userRecord.uid,
        email: userRecord.email || '',
        displayName: userRecord.displayName,
        role: role as 'admin' | 'user',
        createdAt: userRecord.metadata.creationTime,
        lastLoginAt: userRecord.metadata.lastSignInTime
      });
    }

    return { users };
  } catch (error) {
    console.error('Error getting users:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get users');
  }
});

// Note: User document creation is handled in the client-side auth context
// This ensures better error handling and immediate feedback to the user

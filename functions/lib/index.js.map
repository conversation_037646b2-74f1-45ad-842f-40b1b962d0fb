{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8DAAgD;AAChD,8CAA8C;AAC9C,wDAAwD;AACxD,4CAAmD;AAEnD,4BAA4B;AAC5B,IAAA,mBAAa,GAAE,CAAC;AAEhB,MAAM,IAAI,GAAG,IAAA,cAAO,GAAE,CAAC;AACvB,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;AAW1B,6BAA6B;AAChB,QAAA,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;IAClF,iCAAiC;IACjC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACjB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;KACvF;IAED,yBAAyB;IACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;IAE/C,IAAI,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,CAAA,IAAI,YAAY,CAAC,IAAI,KAAK,OAAO,EAAE;QACxD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,mBAAmB,EAAE,gCAAgC,CAAC,CAAC;KAC7F;IAED,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IAE3B,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;QACjB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,2BAA2B,CAAC,CAAC;KACvF;IAED,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,uCAAuC,CAAC,CAAC;KACnG;IAED,IAAI;QACF,oBAAoB;QACpB,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAE9C,oCAAoC;QACpC,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YAC3C,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;SAC5B,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;KAC1B;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,yBAAyB,CAAC,CAAC;KAC7E;AACH,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAChB,QAAA,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;;IAClF,iCAAiC;IACjC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACjB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;KACvF;IAED,yBAAyB;IACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;IAE/C,IAAI,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,CAAA,IAAI,YAAY,CAAC,IAAI,KAAK,OAAO,EAAE;QACxD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,mBAAmB,EAAE,gCAAgC,CAAC,CAAC;KAC7F;IAED,IAAI;QACF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAe,EAAE,CAAC;QAE7B,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,KAAK,EAAE;YAC9C,MAAM,IAAI,GAAG,CAAA,MAAA,UAAU,CAAC,YAAY,0CAAE,IAAI,KAAI,MAAM,CAAC;YAErD,KAAK,CAAC,IAAI,CAAC;gBACT,GAAG,EAAE,UAAU,CAAC,GAAG;gBACnB,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;gBAC7B,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,IAAI,EAAE,IAAwB;gBAC9B,SAAS,EAAE,UAAU,CAAC,QAAQ,CAAC,YAAY;gBAC3C,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC,cAAc;aAChD,CAAC,CAAC;SACJ;QAED,OAAO,EAAE,KAAK,EAAE,CAAC;KAClB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;KACzE;AACH,CAAC,CAAC,CAAC;AAEH,0EAA0E;AAC1E,wEAAwE"}
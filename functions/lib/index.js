"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllUsers = exports.setUserRole = void 0;
const functions = __importStar(require("firebase-functions"));
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const app_1 = require("firebase-admin/app");
// Initialize Firebase Admin
(0, app_1.initializeApp)();
const auth = (0, auth_1.getAuth)();
const db = (0, firestore_1.getFirestore)();
// Set user role (admin only)
exports.setUserRole = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    // Check if user is admin
    const callerRecord = await auth.getUser(context.auth.uid);
    const callerClaims = callerRecord.customClaims;
    if (!(callerClaims === null || callerClaims === void 0 ? void 0 : callerClaims.role) || callerClaims.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can set user roles');
    }
    const { uid, role } = data;
    if (!uid || !role) {
        throw new functions.https.HttpsError('invalid-argument', 'uid and role are required');
    }
    if (!['admin', 'user'].includes(role)) {
        throw new functions.https.HttpsError('invalid-argument', 'role must be either "admin" or "user"');
    }
    try {
        // Set custom claims
        await auth.setCustomUserClaims(uid, { role });
        // Update user document in Firestore
        await db.collection('users').doc(uid).update({
            role,
            updatedAt: new Date().toISOString(),
            updatedBy: context.auth.uid
        });
        return { success: true };
    }
    catch (error) {
        console.error('Error setting user role:', error);
        throw new functions.https.HttpsError('internal', 'Failed to set user role');
    }
});
// Get all users (admin only)
exports.getAllUsers = functions.https.onCall(async (data, context) => {
    var _a;
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    // Check if user is admin
    const callerRecord = await auth.getUser(context.auth.uid);
    const callerClaims = callerRecord.customClaims;
    if (!(callerClaims === null || callerClaims === void 0 ? void 0 : callerClaims.role) || callerClaims.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Only admins can view all users');
    }
    try {
        const listUsersResult = await auth.listUsers();
        const users = [];
        for (const userRecord of listUsersResult.users) {
            const role = ((_a = userRecord.customClaims) === null || _a === void 0 ? void 0 : _a.role) || 'user';
            users.push({
                uid: userRecord.uid,
                email: userRecord.email || '',
                displayName: userRecord.displayName,
                role: role,
                createdAt: userRecord.metadata.creationTime,
                lastLoginAt: userRecord.metadata.lastSignInTime
            });
        }
        return { users };
    }
    catch (error) {
        console.error('Error getting users:', error);
        throw new functions.https.HttpsError('internal', 'Failed to get users');
    }
});
// Note: User document creation is handled in the client-side auth context
// This ensures better error handling and immediate feedback to the user
//# sourceMappingURL=index.js.map
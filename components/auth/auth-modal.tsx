"use client"

import { useState } from 'react'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { LoginForm } from './login-form'
import { SignupForm } from './signup-form'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  defaultMode?: 'login' | 'signup'
  redirectTo?: string
}

export function AuthModal({ 
  isOpen, 
  onClose, 
  defaultMode = 'login',
  redirectTo = '/dashboard'
}: AuthModalProps) {
  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)

  const handleClose = () => {
    onClose()
    // Reset to default mode when closing
    setTimeout(() => setMode(defaultMode), 200)
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md p-0 gap-0">
        {mode === 'login' ? (
          <LoginForm
            onSwitchToSignup={() => setMode('signup')}
            redirectTo={redirectTo}
          />
        ) : (
          <SignupForm
            onSwitchToLogin={() => setMode('login')}
            redirectTo={redirectTo}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

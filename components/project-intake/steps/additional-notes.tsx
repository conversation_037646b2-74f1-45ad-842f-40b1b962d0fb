import { UseFormReturn } from "react-hook-form"
import { FileText, MessageSquare } from "lucide-react"

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { ProjectIntakeFormData } from "@/lib/validations/project-intake"

interface AdditionalNotesStepProps {
  form: UseFormReturn<ProjectIntakeFormData>
}

export function AdditionalNotesStep({ form }: AdditionalNotesStepProps) {
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="specialRequirements"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Any special requirements or constraints?
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Mention any specific technologies, compliance requirements, accessibility needs, or other constraints..."
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <p className="text-sm text-muted-foreground">
              Include technical constraints, regulatory requirements, or specific standards to follow
            </p>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="additionalInfo"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Other information you'd like to share
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Share any additional context, inspiration, or questions you have about the project..."
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <p className="text-sm text-muted-foreground">
              Anything else that might help us understand your vision better
            </p>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
          🚀 Ready to bring your vision to life?
        </h4>
        <div className="text-sm text-gray-700 dark:text-gray-200 space-y-2">
          <p>
            <strong>You're almost done!</strong> Once you submit this form, our team at AgneX Studios will:
          </p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Review your requirements within 24 hours</li>
            <li>Prepare a detailed project proposal</li>
            <li>Schedule a consultation call to discuss your vision</li>
            <li>Provide timeline and cost estimates</li>
          </ul>
        </div>
      </div>

      <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg border">
        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          📞 AgneX Studios Contact Information
        </h4>
        <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
          <p><strong>Website:</strong> https://www.agnextech.com</p>
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Phone:</strong> +91 91888 78022</p>
          <p><strong>GSTIN:</strong> 32COLPG6895J1ZE</p>
        </div>
      </div>
    </div>
  )
} 
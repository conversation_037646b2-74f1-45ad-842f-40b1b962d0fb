import { UseFormReturn } from "react-hook-form"
import { <PERSON>, Target, UserCheck } from "lucide-react"

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ProjectIntakeFormData } from "@/lib/validations/project-intake"

interface UserAudienceStepProps {
  form: UseFormReturn<ProjectIntakeFormData>
}

export function UserAudienceStep({ form }: UserAudienceStepProps) {
  const watchedTargetAudience = form.watch("targetAudience") || ""

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="estimatedUsers"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Estimated Number of Users *
            </FormLabel>
            <FormControl>
              <Input 
                placeholder="e.g., 1,000-10,000 users, 500+ daily active users" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="targetAudience"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              Target Audience *
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe your target audience: demographics, interests, tech-savviness, etc."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                Minimum 10 characters required
              </p>
              <span className={`text-xs ${watchedTargetAudience.length >= 10 ? 'text-green-600' : 'text-orange-600'}`}>
                {watchedTargetAudience.length}/10
              </span>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="userRoles"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <UserCheck className="w-4 h-4" />
              User Roles (if any)
            </FormLabel>
            <FormControl>
              <Input 
                placeholder="e.g., Admin, Customer, Vendor, Guest, etc." 
                {...field} 
              />
            </FormControl>
            <p className="text-sm text-muted-foreground">
              Specify different user types and their access levels
            </p>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
        <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
          Understanding Your Users
        </h4>
        <p className="text-sm text-green-700 dark:text-green-200">
          Clear user definition helps us design the perfect user experience and 
          implement appropriate features for each user type.
        </p>
      </div>
    </div>
  )
} 
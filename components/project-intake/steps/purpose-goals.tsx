import { UseFormReturn } from "react-hook-form"
import { Target, Lightbulb, Zap } from "lucide-react"

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { ProjectIntakeFormData } from "@/lib/validations/project-intake"

interface PurposeGoalsStepProps {
  form: UseFormReturn<ProjectIntakeFormData>
}

export function PurposeGoalsStep({ form }: PurposeGoalsStepProps) {
  const watchedProblemSolved = form.watch("problemSolved") || ""
  const watchedCoreFeatures = form.watch("coreFeatures") || ""
  const watchedMainGoal = form.watch("mainGoal") || ""

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="problemSolved"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <Lightbulb className="w-4 h-4" />
              What problem does the app solve? *
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe the main problem or pain point your app/website will address..."
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                Minimum 10 characters required
              </p>
              <span className={`text-xs ${watchedProblemSolved.length >= 10 ? 'text-green-600' : 'text-orange-600'}`}>
                {watchedProblemSolved.length}/10
              </span>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="coreFeatures"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              Core Features Required *
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="List the essential features: User login, Payment Gateway, Chat, Booking System, Dashboard, etc."
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                Include must-have features for your MVP (minimum 10 characters)
              </p>
              <span className={`text-xs ${watchedCoreFeatures.length >= 10 ? 'text-green-600' : 'text-orange-600'}`}>
                {watchedCoreFeatures.length}/10
              </span>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="mainGoal"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              What is the main goal of the app/website? *
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe the primary objective: increase sales, improve efficiency, provide service, etc."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                Minimum 10 characters required
              </p>
              <span className={`text-xs ${watchedMainGoal.length >= 10 ? 'text-green-600' : 'text-orange-600'}`}>
                {watchedMainGoal.length}/10
              </span>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
        <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
          Feature Examples
        </h4>
        <div className="text-sm text-purple-700 dark:text-purple-200 space-y-1">
          <p><strong>Authentication:</strong> User registration, login, password reset</p>
          <p><strong>E-commerce:</strong> Product catalog, shopping cart, payment integration</p>
          <p><strong>Social:</strong> User profiles, messaging, feeds, notifications</p>
          <p><strong>Business:</strong> Dashboard, analytics, reporting, admin panel</p>
        </div>
      </div>
    </div>
  )
} 
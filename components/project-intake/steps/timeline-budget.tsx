import { UseFormReturn } from "react-hook-form"
import { Calendar, DollarSign } from "lucide-react"

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { ProjectIntakeFormData } from "@/lib/validations/project-intake"

interface TimelineBudgetStepProps {
  form: UseFormReturn<ProjectIntakeFormData>
}

export function TimelineBudgetStep({ form }: TimelineBudgetStepProps) {
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="preferredLaunchDate"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Preferred Launch Date *
            </FormLabel>
            <FormControl>
              <Input 
                type="date" 
                {...field} 
              />
            </FormControl>
            <p className="text-sm text-muted-foreground">
              When would you like your project to be completed?
            </p>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="estimatedBudgetRange"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Estimated Budget Range *
            </FormLabel>
            <FormControl>
              <Input 
                placeholder="e.g., $5,000 - $10,000, ₹2,00,000 - ₹5,00,000" 
                {...field} 
              />
            </FormControl>
            <p className="text-sm text-muted-foreground">
              Provide your budget range to help us suggest the best solution
            </p>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            💡 Timeline Tips
          </h4>
          <div className="text-sm text-blue-700 dark:text-blue-200 space-y-1">
            <p>• Simple apps: 4-8 weeks</p>
            <p>• Medium complexity: 8-16 weeks</p>
            <p>• Complex apps: 16+ weeks</p>
            <p>• Allow buffer time for testing</p>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
            💰 Budget Guidelines
          </h4>
          <div className="text-sm text-green-700 dark:text-green-200 space-y-1">
            <p>• MVP: Starting from $5K</p>
            <p>• Standard app: $10K - $50K</p>
            <p>• Enterprise: $50K+</p>
            <p>• Maintenance: 15-20% annually</p>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 dark:bg-yellow-950/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
        <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
          📋 What Affects Timeline & Budget?
        </h4>
        <div className="text-sm text-yellow-700 dark:text-yellow-200 space-y-1">
          <p><strong>Complexity:</strong> Number of features, integrations, and custom requirements</p>
          <p><strong>Design:</strong> Custom UI/UX design vs. template-based approach</p>
          <p><strong>Platforms:</strong> Single platform vs. multi-platform development</p>
          <p><strong>Team Size:</strong> Dedicated team vs. shared resources</p>
        </div>
      </div>
    </div>
  )
} 
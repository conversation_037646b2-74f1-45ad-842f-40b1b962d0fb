import { UseFormReturn } from "react-hook-form"
import { <PERSON>, Plug, <PERSON><PERSON> } from "lucide-react"

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ProjectIntakeFormData } from "@/lib/validations/project-intake"

interface TechnicalRequirementsStepProps {
  form: UseFormReturn<ProjectIntakeFormData>
}

export function TechnicalRequirementsStep({ form }: TechnicalRequirementsStepProps) {
  const watchedRequiresThirdParty = form.watch("requiresThirdPartyIntegrations")
  const watchedHasDesignReferences = form.watch("hasDesignReferences")

  return (
    <div className="space-y-8">
      {/* Authentication */}
      <FormField
        control={form.control}
        name="requiresAuth"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel className="flex items-center gap-2 text-base font-semibold">
              <Shield className="w-4 h-4" />
              Does the app require user authentication (login/sign-up)? *
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={(value) => field.onChange(value === "true")}
                value={field.value?.toString()}
                className="flex flex-col space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="auth-yes" />
                  <Label htmlFor="auth-yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="auth-no" />
                  <Label htmlFor="auth-no">No</Label>
                </div>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Third-party Integrations */}
      <FormField
        control={form.control}
        name="requiresThirdPartyIntegrations"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel className="flex items-center gap-2 text-base font-semibold">
              <Plug className="w-4 h-4" />
              Will there be third-party integrations? *
            </FormLabel>
            <p className="text-sm text-muted-foreground">
              e.g., payment gateways, maps, social login, email services, analytics
            </p>
            <FormControl>
              <RadioGroup
                onValueChange={(value) => field.onChange(value === "true")}
                value={field.value?.toString()}
                className="flex flex-col space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="integrations-yes" />
                  <Label htmlFor="integrations-yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="integrations-no" />
                  <Label htmlFor="integrations-no">No</Label>
                </div>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {watchedRequiresThirdParty && (
        <FormField
          control={form.control}
          name="thirdPartyIntegrationsDetails"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Please specify the integrations needed:</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="e.g., Stripe for payments, Google Maps for location, Firebase Auth for social login..."
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {/* Design References */}
      <FormField
        control={form.control}
        name="hasDesignReferences"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel className="flex items-center gap-2 text-base font-semibold">
              <Palette className="w-4 h-4" />
              Do you have design references or preferences? *
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={(value) => field.onChange(value === "true")}
                value={field.value?.toString()}
                className="flex flex-col space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="true" id="design-yes" />
                  <Label htmlFor="design-yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="false" id="design-no" />
                  <Label htmlFor="design-no">No</Label>
                </div>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {watchedHasDesignReferences && (
        <FormField
          control={form.control}
          name="designReferencesDetails"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Please describe your design preferences or attach references:</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Share links to designs you like, describe your preferred style, colors, or specific requirements..."
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
        <h4 className="font-medium text-orange-900 dark:text-orange-100 mb-2">
          Popular Integrations
        </h4>
        <div className="text-sm text-orange-700 dark:text-orange-200 space-y-1">
          <p><strong>Payment:</strong> Stripe, PayPal, Razorpay, Square</p>
          <p><strong>Maps:</strong> Google Maps, Mapbox</p>
          <p><strong>Authentication:</strong> Google, Facebook, Apple Sign-in</p>
          <p><strong>Communication:</strong> Twilio, SendGrid, Mailchimp</p>
        </div>
      </div>
    </div>
  )
} 
import { UseFormReturn } from "react-hook-form"
import { Smartphone, Globe, Monitor, RefreshCw } from "lucide-react"

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ProjectIntakeFormData } from "@/lib/validations/project-intake"

interface PlatformDetailsStepProps {
  form: UseFormReturn<ProjectIntakeFormData>
}

const productTypes = [
  { id: "mobile-app", label: "Mobile App", icon: Smartphone },
  { id: "web-app", label: "Web App", icon: Globe },
  { id: "both", label: "Both", icon: Monitor },
  { id: "other", label: "Other", icon: RefreshCw },
]

const targetPlatforms = [
  { id: "ios", label: "iOS" },
  { id: "android", label: "Android" },
  { id: "web-desktop", label: "Web (Desktop)" },
  { id: "web-mobile", label: "Web (Mobile Responsive)" },
]

export function PlatformDetailsStep({ form }: PlatformDetailsStepProps) {
  const watchedProductType = form.watch("productType")
  const showOtherField = watchedProductType?.includes("other")

  return (
    <div className="space-y-8">
      {/* Product Type */}
      <FormField
        control={form.control}
        name="productType"
        render={() => (
          <FormItem>
            <FormLabel className="text-base font-semibold">Type of Product *</FormLabel>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              {productTypes.map((type) => {
                const Icon = type.icon
                return (
                  <FormField
                    key={type.id}
                    control={form.control}
                    name="productType"
                    render={({ field }) => {
                      return (
                        <FormItem
                          key={type.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(type.id)}
                              onCheckedChange={(checked) => {
                                return checked
                                  ? field.onChange([...field.value, type.id])
                                  : field.onChange(
                                      field.value?.filter(
                                        (value) => value !== type.id
                                      )
                                    )
                              }}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <div className="flex items-center gap-2">
                              <Icon className="w-4 h-4" />
                              <FormLabel className="text-sm font-normal">
                                {type.label}
                              </FormLabel>
                            </div>
                          </div>
                        </FormItem>
                      )
                    }}
                  />
                )
              })}
            </div>
            {showOtherField && (
              <FormField
                control={form.control}
                name="productTypeOther"
                render={({ field }) => (
                  <FormItem className="mt-4">
                    <FormLabel>Please specify:</FormLabel>
                    <FormControl>
                      <Input placeholder="Describe other product type" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Target Platforms */}
      <FormField
        control={form.control}
        name="targetPlatforms"
        render={() => (
          <FormItem>
            <FormLabel className="text-base font-semibold">Target Platforms *</FormLabel>
            <div className="grid grid-cols-2 gap-4 mt-4">
              {targetPlatforms.map((platform) => (
                <FormField
                  key={platform.id}
                  control={form.control}
                  name="targetPlatforms"
                  render={({ field }) => {
                    return (
                      <FormItem
                        key={platform.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(platform.id)}
                            onCheckedChange={(checked) => {
                              return checked
                                ? field.onChange([...field.value, platform.id])
                                : field.onChange(
                                    field.value?.filter(
                                      (value) => value !== platform.id
                                    )
                                  )
                            }}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-normal">
                            {platform.label}
                          </FormLabel>
                        </div>
                      </FormItem>
                    )
                  }}
                />
              ))}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* New Product or Redesign */}
      <FormField
        control={form.control}
        name="isNewProduct"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel className="text-base font-semibold">
              Is this a new product or a redesign? *
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-col space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="new" id="new" />
                  <Label htmlFor="new">New Product</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="redesign" id="redesign" />
                  <Label htmlFor="redesign">Redesign/Upgrade</Label>
                </div>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
} 
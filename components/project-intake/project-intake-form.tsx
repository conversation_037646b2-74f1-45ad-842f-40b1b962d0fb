"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight, Send, CheckCircle, Loader2, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form } from "@/components/ui/form"
import { Progress } from "@/components/ui/progress"
import { projectIntakeSchema, type ProjectIntakeFormData } from "@/lib/validations/project-intake"
import { submitProjectIntake } from "@/lib/services/project-intake"

import { GeneralInformationStep } from "./steps/general-information"
import { PlatformDetailsStep } from "./steps/platform-details"
import { UserAudienceStep } from "./steps/user-audience"
import { PurposeGoalsStep } from "./steps/purpose-goals"
import { TechnicalRequirementsStep } from "./steps/technical-requirements"
// import { TimelineBudgetStep } from "./steps/timeline-budget"
import { AdditionalNotesStep } from "./steps/additional-notes"

const steps = [
  { 
    id: 1, 
    title: "General Information", 
    description: "Basic project details",
    requiredFields: ["projectName", "clientCompanyName", "pointOfContact", "contactEmail", "contactPhone", "dateOfSubmission"]
  },
  { 
    id: 2, 
    title: "Platform Details", 
    description: "Technical specifications",
    requiredFields: ["productType", "targetPlatforms", "isNewProduct"]
  },
  { 
    id: 3, 
    title: "User & Audience", 
    description: "Target users and roles",
    requiredFields: ["estimatedUsers", "targetAudience"]
  },
  { 
    id: 4, 
    title: "Purpose & Goals", 
    description: "Project objectives",
    requiredFields: ["problemSolved", "coreFeatures", "mainGoal"]
  },
  { 
    id: 5, 
    title: "Technical Requirements", 
    description: "Functional needs",
    requiredFields: ["requiresAuth", "requiresThirdPartyIntegrations", "hasDesignReferences"]
  },
  // { 
  //   id: 6, 
  //   title: "Timeline & Budget", 
  //   description: "Project constraints",
  //   requiredFields: ["preferredLaunchDate", "estimatedBudgetRange"]
  // },
  { 
    id: 6, 
    title: "Additional Notes", 
    description: "Extra requirements",
    requiredFields: [] // Optional step
  },
]

export function ProjectIntakeForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [stepValidationError, setStepValidationError] = useState<string | null>(null)

  const form = useForm<ProjectIntakeFormData>({
    resolver: zodResolver(projectIntakeSchema),
    defaultValues: {
      dateOfSubmission: new Date().toISOString().split('T')[0],
      productType: [],
      targetPlatforms: [],
      requiresAuth: false,
      requiresThirdPartyIntegrations: false,
      hasDesignReferences: false,
      contactEmail: "",
      contactPhone: "",
      // Set default values for hidden fields to prevent validation errors
      preferredLaunchDate: new Date().toISOString().split('T')[0],
      estimatedBudgetRange: "To be discussed",
    },
  })

  const validateCurrentStep = async () => {
    const currentStepConfig = steps[currentStep - 1]
    const requiredFields = currentStepConfig.requiredFields
    
    if (requiredFields.length === 0) return true // Optional step
    
    let isValid = true
    const errors: string[] = []
    
    for (const fieldName of requiredFields) {
      const fieldValue = form.getValues(fieldName as keyof ProjectIntakeFormData)
      
      // Check if field is empty or invalid
      if (fieldValue === undefined || fieldValue === null || fieldValue === "") {
        isValid = false
        errors.push(fieldName)
      } else if (Array.isArray(fieldValue) && fieldValue.length === 0) {
        isValid = false
        errors.push(fieldName)
      } else if (typeof fieldValue === "string" && fieldValue.trim().length === 0) {
        isValid = false
        errors.push(fieldName)
      }
    }
    
    // Additional validation for character minimums
    if (currentStep === 3) {
      const targetAudience = form.getValues("targetAudience")
      if (targetAudience && targetAudience.length < 10) {
        isValid = false
        errors.push("targetAudience (needs 10+ characters)")
      }
    }
    
    if (currentStep === 4) {
      const problemSolved = form.getValues("problemSolved")
      const coreFeatures = form.getValues("coreFeatures")
      const mainGoal = form.getValues("mainGoal")
      
      if (problemSolved && problemSolved.length < 10) {
        isValid = false
        errors.push("problemSolved (needs 10+ characters)")
      }
      if (coreFeatures && coreFeatures.length < 10) {
        isValid = false
        errors.push("coreFeatures (needs 10+ characters)")
      }
      if (mainGoal && mainGoal.length < 10) {
        isValid = false
        errors.push("mainGoal (needs 10+ characters)")
      }
    }
    
    if (!isValid) {
      setStepValidationError(`Please complete all required fields in this step: ${errors.join(", ")}`)
      return false
    }
    
    setStepValidationError(null)
    return true
  }

  const nextStep = async () => {
    const isValid = await validateCurrentStep()
    if (!isValid) return
    
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      setStepValidationError(null)
    }
  }

  const onSubmit = async (data: ProjectIntakeFormData) => {
    console.log('Form submitted with data:', data)
    setIsSubmitting(true)
    setSubmitError(null)
    
    try {
      await submitProjectIntake(data)
      console.log('Submission successful')
      setIsSubmitted(true)
    } catch (error) {
      console.error('Submission error:', error)
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit form. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const onError = (errors: any) => {
    console.log('Form validation errors:', errors)
    setSubmitError('Please fill in all required fields correctly.')
  }

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-2xl mx-auto text-center py-12"
      >
        <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
        <h2 className="text-3xl font-bold mb-4">Thank You!</h2>
        <p className="text-muted-foreground mb-6">
          Your project consultation request has been successfully submitted. Our team at AgneX Studios 
          will review your requirements and reach out within 1-2 business days.
        </p>
        <div className="bg-muted p-4 rounded-lg">
          <p className="text-sm">
            <strong>What happens next?</strong><br />
            Our experts will analyze your requirements and prepare a detailed proposal 
            tailored to your project needs.
          </p>
        </div>
      </motion.div>
    )
  }

  const progress = (currentStep / steps.length) * 100
  const { formState } = form

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Progress Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Project Consultation</h2>
          <span className="text-sm text-muted-foreground">
            Step {currentStep} of {steps.length}
          </span>
        </div>
        <Progress value={progress} className="w-full" />
        
        {/* Step Indicators */}
        <div className="flex justify-between text-xs">
          {steps.map((step) => (
            <div
              key={step.id}
              className={`text-center flex-1 ${
                step.id <= currentStep ? "text-primary" : "text-muted-foreground"
              }`}
            >
              <div className="hidden sm:block">{step.title}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Validation Error */}
      {stepValidationError && (
        <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-orange-600" />
            <p className="text-orange-700 dark:text-orange-200 text-sm">{stepValidationError}</p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {submitError && (
        <div className="bg-red-50 dark:bg-red-950/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <p className="text-red-700 dark:text-red-200 text-sm">{submitError}</p>
          </div>
        </div>
      )}

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle>{steps[currentStep - 1].title}</CardTitle>
          <CardDescription>{steps[currentStep - 1].description}</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit, onError)} className="space-y-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  {currentStep === 1 && <GeneralInformationStep form={form} />}
                  {currentStep === 2 && <PlatformDetailsStep form={form} />}
                  {currentStep === 3 && <UserAudienceStep form={form} />}
                  {currentStep === 4 && <PurposeGoalsStep form={form} />}
                  {currentStep === 5 && <TechnicalRequirementsStep form={form} />}
                  {currentStep === 6 && <AdditionalNotesStep form={form} />}
                </motion.div>
              </AnimatePresence>

              {/* Debug Info (remove in production) */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 p-2 bg-gray-100 dark:bg-gray-800 rounded">
                  Form Valid: {formState.isValid ? 'Yes' : 'No'} | 
                  Errors: {Object.keys(formState.errors).length} | 
                  Current Step: {currentStep}
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>

                {currentStep === steps.length ? (
                  <Button 
                    type="submit" 
                    disabled={isSubmitting}
                    onClick={() => {
                      console.log('Submit button clicked')
                      console.log('Form state:', formState)
                      console.log('Form errors:', formState.errors)
                    }}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Submit
                      </>
                    )}
                  </Button>
                ) : (
                  <Button type="button" onClick={nextStep}>
                    Next
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
} 
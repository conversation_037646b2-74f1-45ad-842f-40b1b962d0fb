"use client"

import React, { useEffect, useState } from 'react'

interface DiagnosticInfo {
  webGLSupported: boolean
  webGL2Supported: boolean
  renderer: string
  vendor: string
  version: string
  maxTextureSize: number
  maxViewportDims: number[]
  extensions: string[]
  devicePixelRatio: number
  browserInfo: string
  errors: string[]
  warnings: string[]
  contextLossEvents: number
}

export function ThreeDiagnostics() {
  const [diagnostics, setDiagnostics] = useState<DiagnosticInfo | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const errors: string[] = []
    const warnings: string[] = []
    let contextLossEvents = 0

    // Monitor for WebGL context loss
    const handleContextLoss = () => {
      contextLossEvents++
      errors.push(`WebGL context lost at ${new Date().toISOString()}`)
    }

    // Monitor console errors
    const originalError = console.error
    const originalWarn = console.warn

    console.error = (...args) => {
      const message = args.join(' ')
      if (message.includes('THREE') || message.includes('WebGL') || message.includes('Context')) {
        errors.push(`${new Date().toISOString()}: ${message}`)
      }
      originalError(...args)
    }

    console.warn = (...args) => {
      const message = args.join(' ')
      if (message.includes('THREE') || message.includes('WebGL') || message.includes('Context')) {
        warnings.push(`${new Date().toISOString()}: ${message}`)
      }
      originalWarn(...args)
    }

    // Gather diagnostic information
    const gatherDiagnostics = (): DiagnosticInfo => {
      let webGLSupported = false
      let webGL2Supported = false
      let renderer = 'Unknown'
      let vendor = 'Unknown'
      let version = 'Unknown'
      let maxTextureSize = 0
      let maxViewportDims = [0, 0]
      let extensions: string[] = []

      try {
        const canvas = document.createElement('canvas')
        
                 // Test WebGL 1.0
         const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext
         if (gl) {
           webGLSupported = true
           const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
           if (debugInfo) {
             renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'Unknown'
             vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) || 'Unknown'
           }
           version = gl.getParameter(gl.VERSION) || 'Unknown'
           maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE) || 0
           maxViewportDims = gl.getParameter(gl.MAX_VIEWPORT_DIMS) || [0, 0]
           extensions = gl.getSupportedExtensions() || []

          // Add context loss listener
          canvas.addEventListener('webglcontextlost', handleContextLoss)
        }

                 // Test WebGL 2.0
         const gl2 = canvas.getContext('webgl2') as WebGL2RenderingContext
         if (gl2) {
           webGL2Supported = true
         }
      } catch (e) {
        errors.push(`WebGL diagnostic error: ${e}`)
      }

      return {
        webGLSupported,
        webGL2Supported,
        renderer,
        vendor,
        version,
        maxTextureSize,
        maxViewportDims,
        extensions,
        devicePixelRatio: window.devicePixelRatio || 1,
        browserInfo: navigator.userAgent,
        errors: [...errors],
        warnings: [...warnings],
        contextLossEvents
      }
    }

    setDiagnostics(gatherDiagnostics())

    // Check for keyboard shortcut to show diagnostics
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setIsVisible(prev => !prev)
      }
    }

    document.addEventListener('keydown', handleKeyPress)

    return () => {
      console.error = originalError
      console.warn = originalWarn
      document.removeEventListener('keydown', handleKeyPress)
    }
  }, [])

  if (!isVisible || !diagnostics) {
    return (
      <div className="fixed bottom-4 right-4 text-xs text-gray-500 bg-black/20 px-2 py-1 rounded">
        Press Ctrl+Shift+D for diagnostics
      </div>
    )
  }

  const hasIssues = diagnostics.errors.length > 0 || diagnostics.warnings.length > 0 || !diagnostics.webGLSupported

  return (
    <div className="fixed top-4 right-4 max-w-md max-h-96 overflow-auto bg-black/90 text-white text-xs p-4 rounded-lg border border-gray-700 z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-sm">Three.js Diagnostics</h3>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      <div className={`mb-3 p-2 rounded ${hasIssues ? 'bg-red-900/50' : 'bg-green-900/50'}`}>
        <div className="font-semibold">
          Status: {hasIssues ? '⚠️ Issues Detected' : '✅ All Good'}
        </div>
      </div>

      <div className="space-y-2">
        <div>
          <strong>WebGL Support:</strong>
          <div className="ml-2">
            WebGL 1.0: {diagnostics.webGLSupported ? '✅' : '❌'}<br/>
            WebGL 2.0: {diagnostics.webGL2Supported ? '✅' : '❌'}
          </div>
        </div>

        <div>
          <strong>GPU Info:</strong>
          <div className="ml-2">
            Renderer: {diagnostics.renderer}<br/>
            Vendor: {diagnostics.vendor}<br/>
            Version: {diagnostics.version}
          </div>
        </div>

        <div>
          <strong>Capabilities:</strong>
          <div className="ml-2">
            Max Texture Size: {diagnostics.maxTextureSize}px<br/>
            Max Viewport: {diagnostics.maxViewportDims.join('x')}px<br/>
            Device Pixel Ratio: {diagnostics.devicePixelRatio}
          </div>
        </div>

        <div>
          <strong>Context Loss Events:</strong>
          <div className="ml-2">
            {diagnostics.contextLossEvents === 0 ? '✅ None' : `❌ ${diagnostics.contextLossEvents}`}
          </div>
        </div>

        {diagnostics.errors.length > 0 && (
          <div>
            <strong className="text-red-400">Errors ({diagnostics.errors.length}):</strong>
            <div className="ml-2 max-h-20 overflow-auto bg-red-900/20 p-1 rounded">
              {diagnostics.errors.map((error, i) => (
                <div key={i} className="text-xs break-words">{error}</div>
              ))}
            </div>
          </div>
        )}

        {diagnostics.warnings.length > 0 && (
          <div>
            <strong className="text-yellow-400">Warnings ({diagnostics.warnings.length}):</strong>
            <div className="ml-2 max-h-20 overflow-auto bg-yellow-900/20 p-1 rounded">
              {diagnostics.warnings.map((warning, i) => (
                <div key={i} className="text-xs break-words">{warning}</div>
              ))}
            </div>
          </div>
        )}

        <div>
          <strong>Extensions ({diagnostics.extensions.length}):</strong>
          <details className="ml-2">
            <summary className="cursor-pointer text-blue-400">Show Extensions</summary>
            <div className="max-h-20 overflow-auto bg-gray-900/50 p-1 rounded text-xs">
              {diagnostics.extensions.map((ext, i) => (
                <div key={i}>{ext}</div>
              ))}
            </div>
          </details>
        </div>

        <div>
          <strong>Browser:</strong>
          <div className="ml-2 text-xs break-words">
            {diagnostics.browserInfo}
          </div>
        </div>
      </div>

      <div className="mt-3 pt-2 border-t border-gray-700 text-xs text-gray-400">
        Press Ctrl+Shift+D to toggle
      </div>
    </div>
  )
} 
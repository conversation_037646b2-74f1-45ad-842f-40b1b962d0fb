"use client"

import Image from "next/image"
import { motion } from "framer-motion"
import { CheckCircle } from "lucide-react"

import { Badge } from "@/components/ui/badge"

const features = [
  "Advanced AI technologies delivering smarter, more intuitive experiences",
  "Specialized onboard AI hardware for maximum data privacy and security",
  "Custom machine learning algorithms tailored to your industry needs",
  "Smart automation solutions that reduce costs and drive growth",
  "Industry-leading AI specialists and business strategists",
  "Future-proof solutions that anticipate tomorrow's challenges",
]

export function FeaturesSection() {
  return (
    <section className="py-20 md:py-28 lg:py-32 bg-muted/30">
      <div className="container px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 md:gap-16 lg:gap-20 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true, margin: "-100px" }}
            className="relative"
          >
            <div className="relative z-10 rounded-xl overflow-hidden max-w-[500px] aspect-[4/3] shadow-lg">
              <Image
                src="/images/features-image.jpg"
                alt="AI-powered innovation"
                fill
                sizes="(max-width: 768px) 100vw, 500px"
                className="object-cover"
              />
            </div>
            <div className="absolute -bottom-8 -right-8 w-48 h-48 bg-primary/10 rounded-full -z-10" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true, margin: "-100px" }}
            className="px-4 sm:px-0"
          >
            <Badge className="mb-6">Why Choose Agnex Studio</Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-8">
              Leading the Future With AI-Powered Innovation
            </h2>

            <p className="text-muted-foreground mb-10 text-lg">
              At Agnex Studio, we combine AI expertise with business understanding to create solutions
              that meet today's needs and anticipate tomorrow's challenges, all while prioritizing data privacy.
            </p>

            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li
                  key={feature}
                  className="flex items-start gap-3"
                >
                  <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                  <span className="text-base md:text-lg">{feature}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
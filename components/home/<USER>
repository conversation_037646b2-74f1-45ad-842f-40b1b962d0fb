"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowRight, Brain, Code, Shield, Layers, Database, Cpu } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

const services = [
  {
    icon: <Brain className="h-6 w-6" />,
    title: "AI-Driven Applications",
    description: "Next-generation applications powered by advanced AI technologies that deliver smarter, faster, and more intuitive digital experiences.",
    href: "/services#ai-applications",
    color: "from-purple-500 to-indigo-500",
  },
  {
    icon: <Cpu className="h-6 w-6" />,
    title: "Embedded AI Solutions",
    description: "Specialized hardware solutions with onboard AI processing for sectors requiring the highest levels of data privacy and security.",
    href: "/services#embedded-ai",
    color: "from-blue-500 to-cyan-400",
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Privacy-Focused AI",
    description: "AI solutions with local processing capabilities that ensure sensitive data remains secure and confidential within your organization.",
    href: "/services#privacy-ai",
    color: "from-green-500 to-emerald-400",
  },
  {
    icon: <Code className="h-6 w-6" />,
    title: "Custom AI Development",
    description: "Tailored AI applications and algorithms designed to meet your specific business challenges and operational requirements.",
    href: "/services#custom-ai",
    color: "from-amber-500 to-orange-400",
  },
  {
    icon: <Layers className="h-6 w-6" />,
    title: "Smart Automation",
    description: "Intelligent automation solutions that increase efficiency, reduce costs, and unlock new growth opportunities for your business.",
    href: "/services#smart-automation",
    color: "from-indigo-500 to-violet-400",
  },
  {
    icon: <Database className="h-6 w-6" />,
    title: "Machine Learning Integration",
    description: "Seamlessly integrate machine learning algorithms into your existing systems for enhanced data analysis and predictive capabilities.",
    href: "/services#ml-integration",
    color: "from-red-500 to-pink-400",
  },
]

export function ServicesSection() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <Badge className="mb-4">Our Services</Badge>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">
            AI-Powered Solutions
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Transforming businesses with next-generation artificial intelligence technologies,
            custom-tailored to meet your specific industry needs.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service, index) => (
            <div
              key={service.title}
              className="group relative"
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <div className="relative overflow-hidden rounded-lg border bg-background p-6 shadow-sm transition-all hover:shadow-md">
                <div className="mb-4">
                  <div className={cn(
                    "inline-flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br",
                    service.color
                  )}>
                    {service.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                <p className="text-muted-foreground mb-4">{service.description}</p>
                <Button asChild variant="ghost" className="gap-1 p-0 h-auto font-semibold">
                  <Link href={service.href}>
                    Learn More <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>

                {hoveredIndex === index && (
                  <div
                    className="absolute inset-0 rounded-lg border-2 border-primary"
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button asChild size="lg">
            <Link href="/services">
              Explore All AI Solutions
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
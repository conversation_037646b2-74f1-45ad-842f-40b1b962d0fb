"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Shield, Zap } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ThreeScene } from "./three-scene"

export function HeroSection() {
  return (
    <section className="relative pt-20 pb-32 md:pt-28 md:pb-40 lg:pt-32 lg:pb-48 overflow-hidden min-h-[95vh] flex items-center">
      {/* 3D Background animation */}
      <ThreeScene />

      {/* Gradient overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-background/80 via-background/50 to-background/80 pointer-events-none"></div>

      <div className="container max-w-7xl mx-auto relative z-10 pointer-events-none">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 md:gap-16 lg:gap-20 items-center">
          {/* Left content column */}
          <div className="lg:col-span-7 space-y-8 text-center lg:text-left px-4 sm:px-6 lg:px-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Badge className="mb-4 px-4 py-1.5 text-sm font-medium bg-primary/10 text-primary border-primary/20 backdrop-blur-md">
                <Sparkles className="h-3.5 w-3.5 mr-1.5" />
                Next-Gen AI Solutions
              </Badge>

              <h1 className="text-4xl md:text-5xl xl:text-6xl font-extrabold tracking-tight leading-tight backdrop-blur-sm py-2">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500">
                  AI-Powered Innovation
                </span>{" "}
                <br className="hidden md:block" />
                for the Future
              </h1>
            </motion.div>

            <motion.p
              className="text-lg md:text-xl max-w-xl mx-auto lg:mx-0 text-muted-foreground backdrop-blur-sm py-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Transforming businesses with custom-tailored AI solutions that deliver smarter, faster, and more intuitive digital experiences.
            </motion.p>

            {/* Feature highlights */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8 mb-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="flex items-center gap-3 backdrop-blur-md bg-background/30 p-3 rounded-lg border border-border/50">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <Brain className="h-5 w-5 text-primary" />
                </div>
                <div className="text-sm font-medium">Advanced AI Technologies</div>
              </div>

              <div className="flex items-center gap-3 backdrop-blur-md bg-background/30 p-3 rounded-lg border border-border/50">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Shield className="h-5 w-5 text-blue-500" />
                </div>
                <div className="text-sm font-medium">Data Privacy Focus</div>
              </div>

              <div className="flex items-center gap-3 backdrop-blur-md bg-background/30 p-3 rounded-lg border border-border/50">
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Zap className="h-5 w-5 text-purple-500" />
                </div>
                <div className="text-sm font-medium">Smart Automation</div>
              </div>
            </motion.div>

            <motion.div
              className="flex flex-wrap gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Button asChild size="lg" className="gap-2 rounded-full px-6 shadow-lg pointer-events-auto">
                <Link href="/services">
                  Explore AI Solutions
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="backdrop-blur-md border-primary/20 hover:bg-primary/10 rounded-full px-6 pointer-events-auto">
                <Link href="/contact">
                  Request Consultation
                </Link>
              </Button>
            </motion.div>
          </div>

          {/* Right image column */}
          <motion.div
            className="lg:col-span-5 relative px-4 sm:px-6 lg:px-0"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, delay: 0.3 }}
          >
            <div className="relative w-full max-w-[550px] aspect-[4/3] mx-auto">
              {/* Decorative elements */}
              <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-primary/10 rounded-full blur-3xl"></div>

              {/* Interactive 3D floating element effect */}
              <motion.div
                animate={{
                  y: [0, 15, 0],
                }}
                transition={{
                  repeat: Infinity,
                  duration: 6,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <div className="absolute inset-0 bg-gradient-to-tr from-primary/30 to-blue-500/30 rounded-full -z-10 blur-3xl"></div>

                {/* Multiple SVG images with transparency for a layered effect */}
                <div className="relative w-full h-full">
                  {/* Background neural network */}
                  <motion.div
                    animate={{
                      opacity: [0.7, 0.9, 0.7],
                      scale: [0.95, 1, 0.95],
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 8,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0"
                  >
                    <Image
                      src="/images/neural-network.svg"
                      alt="Neural network visualization"
                      fill
                      className="object-contain p-4"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 550px"
                      priority
                    />
                  </motion.div>

                  {/* Foreground brain */}
                  <motion.div
                    animate={{
                      opacity: [0.9, 1, 0.9],
                      scale: [0.98, 1.02, 0.98],
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 6,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0"
                  >
                    <Image
                      src="/images/ai-brain.svg"
                      alt="AI-powered brain visualization"
                      fill
                      className="object-contain p-4"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 550px"
                      priority
                    />
                  </motion.div>
                </div>

                {/* Subtle glow effect */}
                <div className="absolute inset-0 bg-gradient-to-tr from-primary/10 to-blue-500/10 rounded-full mix-blend-overlay"></div>
              </motion.div>

              {/* Enhanced floating badges */}
              <motion.div
                className="absolute -right-4 top-1/4 bg-background/80 backdrop-blur-md px-4 py-2 rounded-full shadow-lg border border-primary/20"
                animate={{
                  y: [0, -10, 0],
                  boxShadow: [
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                    "0 10px 15px -3px rgba(79, 70, 229, 0.2), 0 4px 6px -2px rgba(79, 70, 229, 0.1)",
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
                  ]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 5,
                  ease: "easeInOut",
                  delay: 1
                }}
              >
                <div className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-primary" />
                  <span className="text-sm font-medium">Machine Learning</span>
                </div>
              </motion.div>

              <motion.div
                className="absolute -left-4 bottom-1/4 bg-background/80 backdrop-blur-md px-4 py-2 rounded-full shadow-lg border border-blue-500/20"
                animate={{
                  y: [0, 10, 0],
                  boxShadow: [
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                    "0 10px 15px -3px rgba(96, 165, 250, 0.2), 0 4px 6px -2px rgba(96, 165, 250, 0.1)",
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
                  ]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 5,
                  ease: "easeInOut",
                  delay: 0.5
                }}
              >
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-blue-500" />
                  <span className="text-sm font-medium">Data Analytics</span>
                </div>
              </motion.div>

              {/* Additional floating badge */}
              <motion.div
                className="absolute right-1/4 bottom-0 bg-background/80 backdrop-blur-md px-4 py-2 rounded-full shadow-lg border border-purple-500/20"
                animate={{
                  y: [0, -8, 0],
                  boxShadow: [
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                    "0 10px 15px -3px rgba(168, 85, 247, 0.2), 0 4px 6px -2px rgba(168, 85, 247, 0.1)",
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
                  ]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 4,
                  ease: "easeInOut",
                  delay: 1.5
                }}
              >
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-purple-500" />
                  <span className="text-sm font-medium">Neural Networks</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
"use client"

import React, { useRef, useEffect, useState, Suspense } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Stars } from '@react-three/drei'
import { useTheme } from '@/components/theme/theme-provider'

// Check if WebGL is supported
function isWebGLSupported() {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    return !!gl
  } catch (e) {
    return false
  }
}

// Check if we're in browser environment
function isBrowser() {
  return typeof window !== 'undefined' && typeof document !== 'undefined'
}

// Check for problematic browser/GPU combinations
function hasBrowserIssues() {
  if (!isBrowser()) return false
  
  const userAgent = navigator.userAgent.toLowerCase()
  
  // Known problematic combinations
  const problematicPatterns = [
    /firefox.*android/,  // Firefox on Android has WebGL issues
    /chrome.*android.*version\/([0-8])/,  // Old Chrome on Android
    /safari.*version\/([0-9]|1[0-4])/,  // Safari versions < 15
    /trident/,  // Internet Explorer
    /edge\/([0-9]|1[0-7])/,  // Edge Legacy < 18
  ]
  
  return problematicPatterns.some(pattern => pattern.test(userAgent))
}

// Check GPU blacklist
function hasBlacklistedGPU() {
  if (!isBrowser()) return false
  
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext
    if (!gl) return true
    
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
    if (!debugInfo) return false
    
    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)?.toLowerCase() || ''
    
    // Known problematic GPU patterns
    const blacklistedGPUs = [
      /intel.*gma/,  // Intel GMA (very old)
      /microsoft.*basic/,  // Microsoft Basic Render Driver
      /software/,  // Software rendering
      /swiftshader/,  // Software rendering
      /llvmpipe/,  // Software rendering on Linux
    ]
    
    return blacklistedGPUs.some(pattern => pattern.test(renderer))
  } catch (e) {
    return true  // If we can't check, assume problematic
  }
}

// Simple rotating stars - most basic implementation possible
function SimpleStars() {
  const starsRef = useRef<any>(null)
  
  useFrame((state) => {
    if (starsRef.current) {
      starsRef.current.rotation.y = state.clock.elapsedTime * 0.02
    }
  })

  return (
    <group ref={starsRef}>
      <Stars
        radius={50}
        depth={10}
        count={2000}
        factor={2}
        saturation={0}
        fade={false}
        speed={0.5}
      />
    </group>
  )
}

// Basic geometric shape instead of text to avoid font issues
function SimpleGeometry() {
  const meshRef = useRef<any>(null)
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.2
    }
  })

  return (
    <mesh ref={meshRef} position={[0, 0, -5]}>
      <torusGeometry args={[1, 0.3, 8, 16]} />
      <meshBasicMaterial color="#4f46e5" wireframe />
    </mesh>
  )
}

// Minimal scene with only essential components
function MinimalScene() {
  const { theme } = useTheme()
  
  return (
    <>
      <ambientLight intensity={0.3} />
      <SimpleStars />
      <fog attach="fog" args={[theme === 'dark' ? '#000011' : '#f0f0f0', 10, 50]} />
    </>
  )
}

// Completely static fallback
function StaticFallback({ reason }: { reason?: string }) {
  return (
    <div className="absolute inset-0 -z-10 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="absolute inset-0 opacity-20">
        <div className="stars"></div>
      </div>
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-white text-sm">
        Agnex Studio
      </div>
      {reason && process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 left-4 text-xs text-yellow-400 bg-black/50 p-2 rounded">
          Fallback: {reason}
        </div>
      )}
      <style jsx>{`
        .stars {
          background: radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.5), transparent),
                      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.8), transparent),
                      radial-gradient(2px 2px at 160px 30px, #fff, transparent);
          background-repeat: repeat;
          background-size: 200px 100px;
          animation: sparkle 20s linear infinite;
        }
        @keyframes sparkle {
          from { transform: translateX(0); }
          to { transform: translateX(-200px); }
        }
      `}</style>
    </div>
  )
}

// Canvas component wrapper with error handling
function SafeCanvas({ children, theme }: { children: React.ReactNode, theme: string }) {
  const [hasError, setHasError] = useState(false)

  if (hasError || !isBrowser() || !isWebGLSupported()) {
    return <StaticFallback reason="WebGL not supported or error occurred" />
  }

  return (
    <Canvas
      camera={{ position: [0, 0, 5], fov: 60 }}
      dpr={Math.min(window.devicePixelRatio || 1, 2)} // Limit DPR to prevent high resolution issues
      gl={{
        antialias: false,
        alpha: false,
        powerPreference: "default",
        failIfMajorPerformanceCaveat: true, // Fail if performance is bad
        preserveDrawingBuffer: false,
        stencil: false,
        depth: true
      }}
      style={{ 
        background: theme === 'dark' ? '#000011' : '#f0f0f0',
        width: '100%',
        height: '100%'
      }}
      onError={() => setHasError(true)}
      onCreated={(state) => {
        // Additional safety checks on creation
        if (!state.gl || !state.gl.getContext()) {
          setHasError(true)
        }
      }}
    >
      {children}
    </Canvas>
  )
}

// Main component with maximum error protection
export function ThreeScene() {
  const [mounted, setMounted] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [webGLSupported, setWebGLSupported] = useState(false)
  const [fallbackReason, setFallbackReason] = useState<string>('')
  const { theme } = useTheme()

  useEffect(() => {
    // Only mount after hydration is complete
    setMounted(true)
    
    // Check various compatibility issues
    if (!isBrowser()) {
      setFallbackReason('Not in browser environment')
      return
    }
    
    if (!isWebGLSupported()) {
      setFallbackReason('WebGL not supported')
      setWebGLSupported(false)
      return
    }
    
    if (hasBrowserIssues()) {
      setFallbackReason('Browser compatibility issues detected')
      return
    }
    
    if (hasBlacklistedGPU()) {
      setFallbackReason('GPU compatibility issues detected')
      return
    }
    
    setWebGLSupported(true)

    // Add global error handlers
    const handleError = (event: ErrorEvent) => {
      if (event.message && (
        event.message.includes('THREE') || 
        event.message.includes('WebGL') || 
        event.message.includes('Context') ||
        event.message.includes('gl')
      )) {
        setHasError(true)
        setFallbackReason('Runtime error detected')
      }
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason && typeof event.reason === 'string' && (
        event.reason.includes('THREE') || 
        event.reason.includes('WebGL') ||
        event.reason.includes('Context')
      )) {
        setHasError(true)
        setFallbackReason('Promise rejection detected')
      }
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // Don't render until mounted (prevents SSR/hydration issues)
  if (!mounted) {
    return <div className="absolute inset-0 -z-10 bg-slate-900" />
  }

  // Show static fallback if there are any issues
  if (hasError || !webGLSupported || !isBrowser() || fallbackReason) {
    return <StaticFallback reason={fallbackReason} />
  }

  try {
    return (
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <Suspense fallback={<StaticFallback reason="Loading..." />}>
          <SafeCanvas theme={theme}>
            <MinimalScene />
          </SafeCanvas>
        </Suspense>
        
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center">
          <div className="bg-black/30 backdrop-blur-sm rounded-full px-3 py-1 text-white text-xs">
            Agnex Studio
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Three.js render error:', error)
    return <StaticFallback reason="Render error" />
  }
} 
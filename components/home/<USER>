"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { ArrowRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"

export function CTASection() {
  return (
    <section className="py-16 md:py-24 bg-primary text-primary-foreground">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="max-w-3xl mx-auto text-center"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            Ready to Transform Your Business with AI?
          </h2>
          <p className="text-lg md:text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Discover how our AI-driven solutions can help your business increase efficiency, 
            reduce costs, and unlock new growth opportunities while prioritizing data privacy and security.
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <Button 
              asChild 
              variant="secondary" 
              size="lg"
              className="gap-2"
            >
              <Link href="/contact">
                Request AI Consultation <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
            <Button 
              asChild 
              variant="outline" 
              size="lg"
              className="bg-transparent hover:bg-primary-foreground/10 text-primary-foreground"
            >
              <Link href="/services">
                Explore AI Solutions
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
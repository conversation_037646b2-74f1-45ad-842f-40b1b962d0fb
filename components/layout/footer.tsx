"use client"

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { Facebook, Twitter, Instagram, Linkedin, Github, Mail } from "lucide-react"
import { useTheme } from '@/components/theme/theme-provider' // Import useTheme

const footerLinks = [
  {
    title: "Solutions",
    links: [
      { name: "AI Solutions", href: "/services" },
      { name: "Portfolio", href: "/portfolio" },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About", href: "/about" },
      { name: "Contact", href: "/contact" },
    ],
  },
  {
    title: "Legal",
    links: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
    ],
  },
]

export function Footer() {
  const currentYear = new Date().getFullYear()
  const { theme } = useTheme() // Get the current theme
  const [mounted, setMounted] = React.useState(false)

  // Effect to mark component as mounted
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Determine logo source based on theme, with fallback for SSR
  const logoSrc = !mounted ? '/images/logo_light.png' :
                 theme === 'dark' ? '/images/logo_dark.png' : '/images/logo_light.png'

  return (
    <footer className="border-t border-border/40 bg-background">
      <div className="container px-4 sm:px-6 lg:px-8 py-16 md:py-20">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-5">
          <div className="lg:col-span-2 space-y-6">
            <Link href="/" className="flex items-center gap-3 mb-4">
              <div className="relative w-10 h-10">
                <Image
                  src={logoSrc} // Use dynamic logo source
                  alt="Agnex Studio Logo"
                  fill
                  className="object-contain"
                  key={logoSrc} // Add key to force re-render on src change
                />
              </div>
              <span className="font-bold text-xl">Agnex Studio</span>
            </Link>
            <p className="text-muted-foreground max-w-md">
              We build innovative AI-powered solutions that help businesses thrive in the modern world.
            </p>
            <div className="flex gap-3">
              <SocialLink href="https://linkedin.com" aria-label="LinkedIn">
                <Linkedin className="h-5 w-5" />
              </SocialLink>
              <SocialLink href="https://twitter.com" aria-label="Twitter">
                <Twitter className="h-5 w-5" />
              </SocialLink>
              <SocialLink href="https://github.com" aria-label="GitHub">
                <Github className="h-5 w-5" />
              </SocialLink>
            </div>
          </div>

          {footerLinks.map((group) => (
            <div key={group.title} className="space-y-4">
              <h3 className="text-sm font-semibold">{group.title}</h3>
              <ul className="space-y-3">
                {group.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-16 pt-6 border-t border-border/40 flex flex-col md:flex-row items-center justify-between gap-4">
          <p className="text-sm text-muted-foreground">
            &copy; {currentYear} Agnex Studio. All rights reserved.
          </p>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Contact us:</span>
            <Link
              href="mailto:<EMAIL>"
              className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              <Mail className="h-4 w-4" />
              <EMAIL>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}

// Memoized SocialLink component to prevent unnecessary re-renders
const SocialLink = React.memo(function SocialLink({
  href,
  children,
  ...props
}: React.ComponentPropsWithoutRef<typeof Link>) {
  return (
    <Link
      href={href}
      className="flex h-8 w-8 items-center justify-center rounded-md hover:text-primary transition-colors"
      {...props}
    >
      {children}
    </Link>
  )
})
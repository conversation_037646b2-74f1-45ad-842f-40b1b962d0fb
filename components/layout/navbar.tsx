"use client"

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "@/components/theme/theme-provider"; // Use the custom hook

import { Button } from "@/components/ui/button"
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/theme/theme-toggle"

const navItems = [
  { name: "Home", href: "/" },
  { name: "Solutions", href: "/services" },
  { name: "OS", href: "/agnex-os" },
  { name: "Portfolio", href: "/portfolio" },
  { name: "About", href: "/about" },
  { name: "Contact", href: "/contact" },
]

export function Navbar() {
  const pathname = usePathname()
  const [scrolled, setScrolled] = React.useState(false)
  const { theme } = useTheme();
  // State to hold the logo source and track component mounting
  const [mounted, setMounted] = React.useState(false);

  // Effect to mark component as mounted
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Determine the correct logo based on the current theme
  const logoSrc = !mounted ? '/images/logo_light.png' :
                  theme === 'dark' ? '/images/logo_dark.png' : '/images/logo_light.png';

  // Add scroll detection with debounce for better performance
  React.useEffect(() => {
    let scrollTimer: NodeJS.Timeout;

    const handleScroll = () => {
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(() => {
        setScrolled(window.scrollY > 20);
      }, 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      clearTimeout(scrollTimer);
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <nav className={`sticky top-0 z-40 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200 ${
      scrolled ? "py-2" : "py-4"
    }`}>
      <div className="container flex items-center justify-between">
        {/* Logo and Company Name */}
        <div className="flex items-center space-x-3">
          <Link href="/" className="flex items-center gap-3 group">
            <div className="relative w-10 h-10 overflow-hidden rounded-md shadow-sm transition-transform duration-200 group-hover:scale-105">
              <Image
                src={logoSrc}
                alt="Agnex Studio Logo"
                fill
                className="object-contain"
                key={logoSrc} // Add key to force re-render on src change
              />
            </div>
            <span className="font-bold text-xl tracking-tight">
              Agnex Studio
            </span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-1">
          <div className="mr-2 flex items-center">
            {navItems.map((item) => (
              <NavItem
                key={item.href}
                href={item.href}
                isActive={pathname === item.href}
              >
                {item.name}
              </NavItem>
            ))}
          </div>
          <div className="flex items-center gap-3 pl-4 border-l border-border/40">
            <ThemeToggle />
            <Button asChild variant="outline" size="sm">
              <Link href="/contact">Contact</Link>
            </Button>
            <Button asChild variant="default" size="sm" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Link href="/project-intake" className="flex items-center gap-2">
                <Rocket className="w-4 h-4" />
                Get Started
              </Link>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden flex items-center gap-3">
          <ThemeToggle />
          <Sheet>
            <SheetTrigger asChild>
              <Button size="icon" variant="ghost">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle Menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="pr-0">
              <MobileNav pathname={pathname} />
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  )
}

function NavItem({
  href,
  children,
  isActive
}: {
  href: string
  children: React.ReactNode
  isActive?: boolean
}) {
  return (
    <Link
      href={href}
      className={`relative px-3 py-2 text-sm font-medium transition-colors hover:text-foreground/80 group
        ${isActive ? "text-foreground" : "text-foreground/60"}`}
    >
      <span className="relative z-10">{children}</span>
      {isActive ? (
        <motion.div
          className="absolute inset-0 rounded-md bg-primary/10"
          layoutId="navbar-active-indicator"
          transition={{ type: "spring", stiffness: 350, damping: 30 }}
        />
      ) : (
        <div className="absolute inset-0 rounded-md bg-transparent opacity-0 transition-opacity group-hover:bg-primary/5 group-hover:opacity-100" />
      )}
    </Link>
  )
}

function MobileNav({ pathname }: { pathname: string }) {
  const { theme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const logoSrc = !mounted ? '/images/logo_light.png' :
                 theme === 'dark' ? '/images/logo_dark.png' : '/images/logo_light.png';

  return (
    <div className="flex h-full flex-col py-6 pl-6 pr-2">
      <Link href="/" className="flex items-center gap-3 px-2 mb-8">
        <div className="relative w-8 h-8">
          <Image
            src={logoSrc}
            alt="Agnex Studio Logo"
            fill
            className="object-contain"
            key={logoSrc} // Add key to force re-render on src change
          />
        </div>
        <span className="font-bold">Agnex Studio</span>
      </Link>
      <div className="mt-4 flex flex-col gap-1">
        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={`flex w-full items-center py-3 px-3 text-base font-medium transition-colors rounded-md
              ${pathname === item.href
                ? "text-foreground bg-primary/10"
                : "text-foreground/60 hover:text-foreground hover:bg-primary/5"}`}
          >
            {item.name}
          </Link>
        ))}
        <div className="flex items-center justify-between mt-8 mb-4 px-3">
          <span className="text-sm font-medium">Theme</span>
          <ThemeToggle />
        </div>
        <Button asChild variant="outline" className="mt-4 mx-3">
          <Link href="/contact">Contact</Link>
        </Button>
        <Button asChild className="mt-2 mx-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
          <Link href="/project-intake" className="flex items-center gap-2">
            <Rocket className="w-4 h-4" />
            Get Started
          </Link>
        </Button>
      </div>
    </div>
  )
}
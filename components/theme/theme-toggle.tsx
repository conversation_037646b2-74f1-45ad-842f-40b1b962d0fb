"use client"

import * as React from "react"
import { <PERSON>, Sun, <PERSON>pt<PERSON>, Check } from "lucide-react"
import { useTheme } from "./theme-provider"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)
  const [actualTheme, setActualTheme] = React.useState<'light' | 'dark'>('light')

  // Function to get the actual applied theme (resolves 'system')
  const getResolvedTheme = React.useCallback(() => {
    try {
      // First check if we have a stored resolved theme
      const resolvedTheme = localStorage.getItem('agnexstudio-ui-theme-resolved')
      if (resolvedTheme === 'dark' || resolvedTheme === 'light') {
        return resolvedTheme
      }

      // If not, check the document class
      if (document.documentElement.classList.contains('dark')) {
        return 'dark'
      } else if (document.documentElement.classList.contains('light')) {
        return 'light'
      }

      // Fallback to system preference
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    } catch (e) {
      console.error("Error getting resolved theme:", e)
      return 'light'
    }
  }, [])

  // Only show the theme toggle after mounting to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
    setActualTheme(getResolvedTheme())

    // Listen for theme changes
    const handleThemeChange = () => {
      setActualTheme(getResolvedTheme())
    }

    // Listen for theme changes using the custom event
    window.addEventListener('themechange', handleThemeChange)

    // Also listen for system preference changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', handleThemeChange)

    return () => {
      window.removeEventListener('themechange', handleThemeChange)
      mediaQuery.removeEventListener('change', handleThemeChange)
    }
  }, [getResolvedTheme])

  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" className="rounded-full w-9 h-9 opacity-0">
        <span className="sr-only">Loading theme toggle</span>
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full w-9 h-9 relative overflow-hidden"
          onClick={() => {
            // Quick toggle between light and dark
            setTheme(actualTheme === 'dark' ? 'light' : 'dark')
          }}
        >
          {/* Use actualTheme instead of relying on CSS classes */}
          <Sun
            className={`h-[1.2rem] w-[1.2rem] transition-all absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${
              actualTheme === 'dark' ? 'rotate-90 scale-0 opacity-0' : 'rotate-0 scale-100 opacity-100'
            }`}
          />
          <Moon
            className={`h-[1.2rem] w-[1.2rem] transition-all absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ${
              actualTheme === 'dark' ? 'rotate-0 scale-100 opacity-100' : 'rotate-90 scale-0 opacity-0'
            }`}
          />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          className={theme === "light" ? "bg-accent" : ""}
        >
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
          {theme === "light" && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          className={theme === "dark" ? "bg-accent" : ""}
        >
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
          {theme === "dark" && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          className={theme === "system" ? "bg-accent" : ""}
        >
          <Laptop className="mr-2 h-4 w-4" />
          <span>System</span>
          {theme === "system" && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
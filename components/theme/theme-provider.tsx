"use client"

import { createContext, useContext, useEffect, useState } from "react"

type Theme = "dark" | "light" | "system"

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "agnexstudio-ui-theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme)

  useEffect(() => {
    // This script runs on the client side after hydration
    try {
      const savedTheme = localStorage.getItem(storageKey) as Theme | null

      if (savedTheme) {
        setTheme(savedTheme)
      } else {
        // If no saved theme, use default theme
        setTheme(defaultTheme)
      }
    } catch (e) {
      // Fallback to default theme if localStorage is not available
      console.error("Theme initialization error:", e)
      setTheme(defaultTheme)
    }
  }, [defaultTheme, storageKey])

  useEffect(() => {
    try {
      const root = window.document.documentElement

      // Remove previous theme classes
      root.classList.remove("light", "dark")

      // Apply the current theme
      if (theme === "system") {
        const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light"
        root.classList.add(systemTheme)

        // Also store the resolved theme for components that need to know the actual theme
        try {
          localStorage.setItem(`${storageKey}-resolved`, systemTheme)
        } catch (e) {
          // Ignore localStorage errors
        }
      } else {
        root.classList.add(theme)
        // Store the resolved theme
        try {
          localStorage.setItem(`${storageKey}-resolved`, theme)
        } catch (e) {
          // Ignore localStorage errors
        }
      }

      // Force a re-render of components that depend on the theme
      // Use a try-catch to handle potential errors with custom events
      try {
        window.dispatchEvent(new Event('themechange'))
      } catch (e) {
        console.error("Error dispatching theme change event:", e)
      }

      console.log("Theme applied:", theme, "to element:", root)
    } catch (e) {
      console.error("Theme application error:", e)
    }
  }, [theme, storageKey])

  const value = {
    theme,
    setTheme: (newTheme: Theme) => {
      try {
        console.log("Setting theme to:", newTheme)
        localStorage.setItem(storageKey, newTheme)
        setTheme(newTheme)

        // Theme will be applied by the useEffect hook
        // No need to duplicate the logic here
      } catch (e) {
        console.error("Error setting theme:", e)
      }
    },
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")

  return {
    theme: context.theme,
    setTheme: context.setTheme
  };
}
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: { ignoreDuringBuilds: true },
  output: 'export',
  typescript: { ignoreBuildErrors: true },
  images: {
    unoptimized: true,
  },
  // Removed experimental.forceSwcTransforms for Turbopack compatibility
  webpack: (config, { dev, isServer }) => {
    // Better error reporting in development
    if (dev && !isServer) {
      config.optimization.minimizer = [];
    }
    
    return config;
  },
  /* config options here */
};

export default nextConfig;

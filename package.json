{"name": "agnex-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "export": "next build && next export", "start": "next start", "lint": "next lint", "deploy": "firebase deploy --only hosting"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.8.1", "framer-motion": "^12.7.4", "lucide-react": "^0.501.0", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "tailwind-merge": "^3.2.0", "three": "^0.177.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}
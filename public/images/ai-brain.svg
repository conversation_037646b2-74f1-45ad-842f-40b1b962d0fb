<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Brain outline -->
  <path d="M300 100 C420 100, 500 180, 500 300 C500 420, 420 500, 300 500 C180 500, 100 420, 100 300 C100 180, 180 100, 300 100 Z" 
        fill="url(#brainFill)" stroke="url(#brainStroke)" stroke-width="3" />
  
  <!-- Left hemisphere details -->
  <path d="M300 100 C250 120, 200 150, 180 200 C160 250, 150 280, 150 300 C150 320, 160 350, 180 400 C200 450, 250 480, 300 500" 
        fill="none" stroke="url(#hemisphereStroke)" stroke-width="2" />
  
  <!-- Right hemisphere details -->
  <path d="M300 100 C350 120, 400 150, 420 200 C440 250, 450 280, 450 300 C450 320, 440 350, 420 400 C400 450, 350 480, 300 500" 
        fill="none" stroke="url(#hemisphereStroke)" stroke-width="2" />
  
  <!-- <PERSON> folds - left side -->
  <path d="M150 250 C180 240, 200 260, 220 250 C240 240, 260 260, 280 250" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M150 300 C180 290, 200 310, 220 300 C240 290, 260 310, 280 300" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M150 350 C180 340, 200 360, 220 350 C240 340, 260 360, 280 350" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M180 200 C200 190, 220 210, 240 200 C260 190, 280 210, 300 200" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M180 400 C200 390, 220 410, 240 400 C260 390, 280 410, 300 400" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  
  <!-- Brain folds - right side -->
  <path d="M320 250 C340 240, 360 260, 380 250 C400 240, 420 260, 450 250" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M320 300 C340 290, 360 310, 380 300 C400 290, 420 310, 450 300" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M320 350 C340 340, 360 360, 380 350 C400 340, 420 360, 450 350" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M300 200 C320 190, 340 210, 360 200 C380 190, 400 210, 420 200" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  <path d="M300 400 C320 390, 340 410, 360 400 C380 390, 400 410, 420 400" 
        fill="none" stroke="url(#foldsGradient)" stroke-width="2" />
  
  <!-- Neural network nodes -->
  <g>
    <!-- Left hemisphere nodes -->
    <circle cx="200" cy="200" r="8" fill="url(#nodeGradient1)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="180" cy="250" r="8" fill="url(#nodeGradient1)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="220" cy="300" r="8" fill="url(#nodeGradient1)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="180" cy="350" r="8" fill="url(#nodeGradient1)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2.8s" repeatCount="indefinite" />
    </circle>
    <circle cx="240" cy="400" r="8" fill="url(#nodeGradient1)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3.2s" repeatCount="indefinite" />
    </circle>
    
    <!-- Right hemisphere nodes -->
    <circle cx="400" cy="200" r="8" fill="url(#nodeGradient2)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="420" cy="250" r="8" fill="url(#nodeGradient2)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3.8s" repeatCount="indefinite" />
    </circle>
    <circle cx="380" cy="300" r="8" fill="url(#nodeGradient2)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4.2s" repeatCount="indefinite" />
    </circle>
    <circle cx="420" cy="350" r="8" fill="url(#nodeGradient2)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3.3s" repeatCount="indefinite" />
    </circle>
    <circle cx="360" cy="400" r="8" fill="url(#nodeGradient2)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2.7s" repeatCount="indefinite" />
    </circle>
    
    <!-- Central nodes -->
    <circle cx="300" cy="150" r="10" fill="url(#nodeGradient3)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="250" r="10" fill="url(#nodeGradient3)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="350" r="10" fill="url(#nodeGradient3)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="450" r="10" fill="url(#nodeGradient3)">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- Neural connections -->
  <g opacity="0.6">
    <!-- Left hemisphere connections -->
    <path d="M200 200 L300 150" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite" />
    </path>
    <path d="M180 250 L300 250" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4s" repeatCount="indefinite" />
    </path>
    <path d="M220 300 L300 350" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.5s" repeatCount="indefinite" />
    </path>
    <path d="M180 350 L300 450" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.8s" repeatCount="indefinite" />
    </path>
    <path d="M240 400 L300 350" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.2s" repeatCount="indefinite" />
    </path>
    
    <!-- Right hemisphere connections -->
    <path d="M400 200 L300 150" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.5s" repeatCount="indefinite" />
    </path>
    <path d="M420 250 L300 250" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.8s" repeatCount="indefinite" />
    </path>
    <path d="M380 300 L300 350" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.2s" repeatCount="indefinite" />
    </path>
    <path d="M420 350 L300 450" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.3s" repeatCount="indefinite" />
    </path>
    <path d="M360 400 L300 350" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.7s" repeatCount="indefinite" />
    </path>
    
    <!-- Cross connections -->
    <path d="M200 200 L420 250" stroke="url(#connectionGradient3)" stroke-width="1" opacity="0.4">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="5s" repeatCount="indefinite" />
    </path>
    <path d="M180 250 L380 300" stroke="url(#connectionGradient3)" stroke-width="1" opacity="0.4">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4.5s" repeatCount="indefinite" />
    </path>
    <path d="M220 300 L420 350" stroke="url(#connectionGradient3)" stroke-width="1" opacity="0.4">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="5.5s" repeatCount="indefinite" />
    </path>
    <path d="M400 200 L180 350" stroke="url(#connectionGradient3)" stroke-width="1" opacity="0.4">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="6s" repeatCount="indefinite" />
    </path>
    <path d="M420 250 L240 400" stroke="url(#connectionGradient3)" stroke-width="1" opacity="0.4">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="5.2s" repeatCount="indefinite" />
    </path>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="brainFill" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.05" />
      <stop offset="50%" stop-color="#60a5fa" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.05" />
    </linearGradient>
    <linearGradient id="brainStroke" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.8" />
      <stop offset="50%" stop-color="#60a5fa" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="hemisphereStroke" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#60a5fa" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#4f46e5" stop-opacity="0.6" />
    </linearGradient>
    <linearGradient id="foldsGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.4" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.4" />
    </linearGradient>
    <radialGradient id="nodeGradient1" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="1" />
      <stop offset="100%" stop-color="#4f46e5" stop-opacity="0.5" />
    </radialGradient>
    <radialGradient id="nodeGradient2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#f472b6" stop-opacity="1" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.5" />
    </radialGradient>
    <radialGradient id="nodeGradient3" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#60a5fa" stop-opacity="1" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.5" />
    </radialGradient>
    <linearGradient id="connectionGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="connectionGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#f472b6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="connectionGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.5" />
      <stop offset="50%" stop-color="#60a5fa" stop-opacity="0.5" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.5" />
    </linearGradient>
  </defs>
</svg>

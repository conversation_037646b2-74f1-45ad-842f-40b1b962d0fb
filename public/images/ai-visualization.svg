<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="450" viewBox="0 0 600 450" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Neural network connections -->
  <g opacity="0.8">
    <!-- Layer 1 to Layer 2 connections -->
    <path d="M150 100 L300 150" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 150 L300 100" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 100 L300 200" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 150 L300 150" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 200 L300 100" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 200 L300 200" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 250 L300 150" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 250 L300 250" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 300 L300 200" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 300 L300 300" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 350 L300 250" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />
    <path d="M150 350 L300 350" stroke="url(#gradient1)" stroke-width="2" opacity="0.6" />

    <!-- Layer 2 to Layer 3 connections -->
    <path d="M300 100 L450 150" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 150 L450 100" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 150 L450 200" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 200 L450 150" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 200 L450 250" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 250 L450 200" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 250 L450 300" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 300 L450 250" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 300 L450 350" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
    <path d="M300 350 L450 300" stroke="url(#gradient2)" stroke-width="2" opacity="0.6" />
  </g>

  <!-- Neural network nodes -->
  <g>
    <!-- Layer 1 nodes -->
    <circle cx="150" cy="100" r="15" fill="url(#nodeGradient1)" />
    <circle cx="150" cy="150" r="15" fill="url(#nodeGradient1)" />
    <circle cx="150" cy="200" r="15" fill="url(#nodeGradient1)" />
    <circle cx="150" cy="250" r="15" fill="url(#nodeGradient1)" />
    <circle cx="150" cy="300" r="15" fill="url(#nodeGradient1)" />
    <circle cx="150" cy="350" r="15" fill="url(#nodeGradient1)" />

    <!-- Layer 2 nodes -->
    <circle cx="300" cy="100" r="15" fill="url(#nodeGradient2)" />
    <circle cx="300" cy="150" r="15" fill="url(#nodeGradient2)" />
    <circle cx="300" cy="200" r="15" fill="url(#nodeGradient2)" />
    <circle cx="300" cy="250" r="15" fill="url(#nodeGradient2)" />
    <circle cx="300" cy="300" r="15" fill="url(#nodeGradient2)" />
    <circle cx="300" cy="350" r="15" fill="url(#nodeGradient2)" />

    <!-- Layer 3 nodes -->
    <circle cx="450" cy="100" r="15" fill="url(#nodeGradient3)" />
    <circle cx="450" cy="150" r="15" fill="url(#nodeGradient3)" />
    <circle cx="450" cy="200" r="15" fill="url(#nodeGradient3)" />
    <circle cx="450" cy="250" r="15" fill="url(#nodeGradient3)" />
    <circle cx="450" cy="300" r="15" fill="url(#nodeGradient3)" />
    <circle cx="450" cy="350" r="15" fill="url(#nodeGradient3)" />
  </g>

  <!-- Brain outline -->
  <path d="M300 50 C400 50, 450 100, 450 200 C450 300, 400 350, 300 350 C200 350, 150 300, 150 200 C150 100, 200 50, 300 50 Z" 
        fill="none" stroke="url(#brainGradient)" stroke-width="3" opacity="0.7" />

  <!-- Circuit patterns -->
  <path d="M200 100 L250 100 L250 150 L300 150" stroke="#4f46e5" stroke-width="2" opacity="0.5" />
  <path d="M200 200 L220 200 L220 250 L250 250 L250 300 L300 300" stroke="#4f46e5" stroke-width="2" opacity="0.5" />
  <path d="M350 100 L380 100 L380 150 L400 150" stroke="#4f46e5" stroke-width="2" opacity="0.5" />
  <path d="M350 200 L370 200 L370 250 L400 250 L400 300 L420 300" stroke="#4f46e5" stroke-width="2" opacity="0.5" />

  <!-- Glowing dots -->
  <circle cx="250" cy="100" r="4" fill="#60a5fa">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="3s" repeatCount="indefinite" />
  </circle>
  <circle cx="220" cy="200" r="4" fill="#60a5fa">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" />
  </circle>
  <circle cx="250" cy="250" r="4" fill="#60a5fa">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="4s" repeatCount="indefinite" />
  </circle>
  <circle cx="380" cy="100" r="4" fill="#60a5fa">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2.5s" repeatCount="indefinite" />
  </circle>
  <circle cx="370" cy="200" r="4" fill="#60a5fa">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="3.5s" repeatCount="indefinite" />
  </circle>
  <circle cx="400" cy="250" r="4" fill="#60a5fa">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2.8s" repeatCount="indefinite" />
  </circle>

  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.2" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#60a5fa" stop-opacity="0.2" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.8" />
    </linearGradient>
    <radialGradient id="nodeGradient1" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="1" />
      <stop offset="100%" stop-color="#4f46e5" stop-opacity="0.5" />
    </radialGradient>
    <radialGradient id="nodeGradient2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#60a5fa" stop-opacity="1" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.5" />
    </radialGradient>
    <radialGradient id="nodeGradient3" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#f472b6" stop-opacity="1" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.5" />
    </radialGradient>
    <linearGradient id="brainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.8" />
      <stop offset="50%" stop-color="#60a5fa" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.8" />
    </linearGradient>
  </defs>
</svg>

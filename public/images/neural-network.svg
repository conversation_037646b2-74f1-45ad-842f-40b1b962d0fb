<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background glow -->
  <circle cx="300" cy="300" r="250" fill="url(#backgroundGlow)" opacity="0.2" />
  
  <!-- Neural network connections -->
  <g opacity="0.7">
    <!-- Layer 1 to Layer 2 connections -->
    <path d="M150 150 L300 100" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4s" repeatCount="indefinite" />
    </path>
    <path d="M150 225 L300 100" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.5s" repeatCount="indefinite" />
    </path>
    <path d="M150 300 L300 100" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.2s" repeatCount="indefinite" />
    </path>
    <path d="M150 375 L300 100" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.8s" repeatCount="indefinite" />
    </path>
    <path d="M150 450 L300 100" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.5s" repeatCount="indefinite" />
    </path>
    
    <path d="M150 150 L300 200" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.2s" repeatCount="indefinite" />
    </path>
    <path d="M150 225 L300 200" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.3s" repeatCount="indefinite" />
    </path>
    <path d="M150 300 L300 200" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.7s" repeatCount="indefinite" />
    </path>
    <path d="M150 375 L300 200" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.1s" repeatCount="indefinite" />
    </path>
    <path d="M150 450 L300 200" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.9s" repeatCount="indefinite" />
    </path>
    
    <path d="M150 150 L300 300" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.4s" repeatCount="indefinite" />
    </path>
    <path d="M150 225 L300 300" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.6s" repeatCount="indefinite" />
    </path>
    <path d="M150 300 L300 300" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.7s" repeatCount="indefinite" />
    </path>
    <path d="M150 375 L300 300" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.3s" repeatCount="indefinite" />
    </path>
    <path d="M150 450 L300 300" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.6s" repeatCount="indefinite" />
    </path>
    
    <path d="M150 150 L300 400" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.4s" repeatCount="indefinite" />
    </path>
    <path d="M150 225 L300 400" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.8s" repeatCount="indefinite" />
    </path>
    <path d="M150 300 L300 400" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.1s" repeatCount="indefinite" />
    </path>
    <path d="M150 375 L300 400" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.9s" repeatCount="indefinite" />
    </path>
    <path d="M150 450 L300 400" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.5s" repeatCount="indefinite" />
    </path>
    
    <path d="M150 150 L300 500" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.2s" repeatCount="indefinite" />
    </path>
    <path d="M150 225 L300 500" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.7s" repeatCount="indefinite" />
    </path>
    <path d="M150 300 L300 500" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.3s" repeatCount="indefinite" />
    </path>
    <path d="M150 375 L300 500" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.8s" repeatCount="indefinite" />
    </path>
    <path d="M150 450 L300 500" stroke="url(#connectionGradient1)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.5s" repeatCount="indefinite" />
    </path>
    
    <!-- Layer 2 to Layer 3 connections -->
    <path d="M300 100 L450 150" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.3s" repeatCount="indefinite" />
    </path>
    <path d="M300 200 L450 150" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.4s" repeatCount="indefinite" />
    </path>
    <path d="M300 300 L450 150" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.6s" repeatCount="indefinite" />
    </path>
    <path d="M300 400 L450 150" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.7s" repeatCount="indefinite" />
    </path>
    <path d="M300 500 L450 150" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.9s" repeatCount="indefinite" />
    </path>
    
    <path d="M300 100 L450 225" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.1s" repeatCount="indefinite" />
    </path>
    <path d="M300 200 L450 225" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.5s" repeatCount="indefinite" />
    </path>
    <path d="M300 300 L450 225" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.6s" repeatCount="indefinite" />
    </path>
    <path d="M300 400 L450 225" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.2s" repeatCount="indefinite" />
    </path>
    <path d="M300 500 L450 225" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.8s" repeatCount="indefinite" />
    </path>
    
    <path d="M300 100 L450 300" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.7s" repeatCount="indefinite" />
    </path>
    <path d="M300 200 L450 300" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.3s" repeatCount="indefinite" />
    </path>
    <path d="M300 300 L450 300" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.4s" repeatCount="indefinite" />
    </path>
    <path d="M300 400 L450 300" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.5s" repeatCount="indefinite" />
    </path>
    <path d="M300 500 L450 300" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.8s" repeatCount="indefinite" />
    </path>
    
    <path d="M300 100 L450 375" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.2s" repeatCount="indefinite" />
    </path>
    <path d="M300 200 L450 375" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.6s" repeatCount="indefinite" />
    </path>
    <path d="M300 300 L450 375" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.7s" repeatCount="indefinite" />
    </path>
    <path d="M300 400 L450 375" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.3s" repeatCount="indefinite" />
    </path>
    <path d="M300 500 L450 375" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.4s" repeatCount="indefinite" />
    </path>
    
    <path d="M300 100 L450 450" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.9s" repeatCount="indefinite" />
    </path>
    <path d="M300 200 L450 450" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.1s" repeatCount="indefinite" />
    </path>
    <path d="M300 300 L450 450" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.5s" repeatCount="indefinite" />
    </path>
    <path d="M300 400 L450 450" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4.6s" repeatCount="indefinite" />
    </path>
    <path d="M300 500 L450 450" stroke="url(#connectionGradient2)" stroke-width="1.5">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3.2s" repeatCount="indefinite" />
    </path>
  </g>
  
  <!-- Neural network nodes -->
  <g>
    <!-- Layer 1 nodes -->
    <circle cx="150" cy="150" r="12" fill="url(#nodeGradient1)">
      <animate attributeName="r" values="10;12;10" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="150" cy="225" r="12" fill="url(#nodeGradient1)">
      <animate attributeName="r" values="10;12;10" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="150" cy="300" r="12" fill="url(#nodeGradient1)">
      <animate attributeName="r" values="10;12;10" dur="3.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="150" cy="375" r="12" fill="url(#nodeGradient1)">
      <animate attributeName="r" values="10;12;10" dur="4.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="150" cy="450" r="12" fill="url(#nodeGradient1)">
      <animate attributeName="r" values="10;12;10" dur="3.2s" repeatCount="indefinite" />
    </circle>
    
    <!-- Layer 2 nodes -->
    <circle cx="300" cy="100" r="15" fill="url(#nodeGradient2)">
      <animate attributeName="r" values="13;15;13" dur="3.8s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="200" r="15" fill="url(#nodeGradient2)">
      <animate attributeName="r" values="13;15;13" dur="4.2s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="300" r="15" fill="url(#nodeGradient2)">
      <animate attributeName="r" values="13;15;13" dur="3.6s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="400" r="15" fill="url(#nodeGradient2)">
      <animate attributeName="r" values="13;15;13" dur="4.4s" repeatCount="indefinite" />
    </circle>
    <circle cx="300" cy="500" r="15" fill="url(#nodeGradient2)">
      <animate attributeName="r" values="13;15;13" dur="3.3s" repeatCount="indefinite" />
    </circle>
    
    <!-- Layer 3 nodes -->
    <circle cx="450" cy="150" r="12" fill="url(#nodeGradient3)">
      <animate attributeName="r" values="10;12;10" dur="4.1s" repeatCount="indefinite" />
    </circle>
    <circle cx="450" cy="225" r="12" fill="url(#nodeGradient3)">
      <animate attributeName="r" values="10;12;10" dur="3.7s" repeatCount="indefinite" />
    </circle>
    <circle cx="450" cy="300" r="12" fill="url(#nodeGradient3)">
      <animate attributeName="r" values="10;12;10" dur="4.3s" repeatCount="indefinite" />
    </circle>
    <circle cx="450" cy="375" r="12" fill="url(#nodeGradient3)">
      <animate attributeName="r" values="10;12;10" dur="3.9s" repeatCount="indefinite" />
    </circle>
    <circle cx="450" cy="450" r="12" fill="url(#nodeGradient3)">
      <animate attributeName="r" values="10;12;10" dur="4.5s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- Gradients -->
  <defs>
    <radialGradient id="backgroundGlow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#4f46e5" stop-opacity="0" />
    </radialGradient>
    <linearGradient id="connectionGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="connectionGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#60a5fa" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.8" />
    </linearGradient>
    <radialGradient id="nodeGradient1" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="1" />
      <stop offset="100%" stop-color="#4f46e5" stop-opacity="0.5" />
    </radialGradient>
    <radialGradient id="nodeGradient2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#60a5fa" stop-opacity="1" />
      <stop offset="100%" stop-color="#60a5fa" stop-opacity="0.5" />
    </radialGradient>
    <radialGradient id="nodeGradient3" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#f472b6" stop-opacity="1" />
      <stop offset="100%" stop-color="#f472b6" stop-opacity="0.5" />
    </radialGradient>
  </defs>
</svg>

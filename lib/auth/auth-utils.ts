import { httpsCallable } from 'firebase/functions'
import { functions } from '@/lib/firebase'

export interface UserData {
  uid: string
  email: string
  displayName?: string
  role: 'admin' | 'user'
  createdAt: string
  lastLoginAt?: string
}

// Cloud function interfaces
export interface SetUserRoleData {
  uid: string
  role: 'admin' | 'user'
}

export interface GetUsersResponse {
  users: UserData[]
}

// Cloud function calls
export const setUserRole = httpsCallable<SetUserRoleData, { success: boolean }>(functions, 'setUserRole')
export const getAllUsers = httpsCallable<{}, GetUsersResponse>(functions, 'getAllUsers')

// Utility functions
export function isAdmin(userRole: { role: string } | null): boolean {
  return userRole?.role === 'admin'
}

export function formatDate(dateString: string): string {
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return 'Unknown'
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long')
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

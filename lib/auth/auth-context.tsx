"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { 
  User, 
  onAuthStateChanged, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signOut,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth'
import { auth } from '@/lib/firebase'

export interface UserRole {
  role: 'admin' | 'user'
  email: string
  uid: string
  displayName?: string
  createdAt?: string
}

interface AuthContextType {
  user: User | null
  userRole: UserRole | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, displayName?: string) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  refreshUserRole: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)

  const refreshUserRole = async () => {
    if (!user) {
      setUserRole(null)
      return
    }

    try {
      // Get the user's ID token to access custom claims
      const idTokenResult = await user.getIdTokenResult()
      const role = idTokenResult.claims.role as 'admin' | 'user' || 'user'
      
      setUserRole({
        role,
        email: user.email!,
        uid: user.uid,
        displayName: user.displayName || undefined,
        createdAt: idTokenResult.claims.createdAt as string || undefined
      })
    } catch (error) {
      console.error('Error fetching user role:', error)
      // Default to user role if there's an error
      setUserRole({
        role: 'user',
        email: user.email!,
        uid: user.uid,
        displayName: user.displayName || undefined
      })
    }
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user)
      if (user) {
        await refreshUserRole()
      } else {
        setUserRole(null)
      }
      setLoading(false)
    })

    return unsubscribe
  }, [])

  // Refresh user role when user changes
  useEffect(() => {
    if (user) {
      refreshUserRole()
    }
  }, [user])

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password)
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign in')
    }
  }

  const signUp = async (email: string, password: string, displayName?: string) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password)
      
      if (displayName && result.user) {
        await updateProfile(result.user, { displayName })
      }
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create account')
    }
  }

  const logout = async () => {
    try {
      await signOut(auth)
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign out')
    }
  }

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email)
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send password reset email')
    }
  }

  const value: AuthContextType = {
    user,
    userRole,
    loading,
    signIn,
    signUp,
    logout,
    resetPassword,
    refreshUserRole
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

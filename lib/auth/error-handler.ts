// Firebase Auth error codes and user-friendly messages
export const AUTH_ERROR_MESSAGES: Record<string, string> = {
  'auth/user-not-found': 'No account found with this email address.',
  'auth/wrong-password': 'Incorrect password. Please try again.',
  'auth/invalid-email': 'Please enter a valid email address.',
  'auth/user-disabled': 'This account has been disabled. Please contact support.',
  'auth/email-already-in-use': 'An account with this email already exists.',
  'auth/weak-password': 'Password should be at least 6 characters long.',
  'auth/operation-not-allowed': 'This operation is not allowed. Please contact support.',
  'auth/invalid-credential': 'Invalid email or password. Please check your credentials.',
  'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
  'auth/network-request-failed': 'Network error. Please check your connection and try again.',
  'auth/popup-closed-by-user': 'Sign-in was cancelled.',
  'auth/cancelled-popup-request': 'Sign-in was cancelled.',
  'auth/popup-blocked': 'Pop-up was blocked by your browser. Please allow pop-ups and try again.',
  'auth/invalid-api-key': 'Invalid API key. Please contact support.',
  'auth/app-deleted': 'This app has been deleted. Please contact support.',
  'auth/expired-action-code': 'This link has expired. Please request a new one.',
  'auth/invalid-action-code': 'This link is invalid. Please request a new one.',
  'auth/invalid-continue-uri': 'Invalid redirect URL.',
  'auth/missing-continue-uri': 'Missing redirect URL.',
  'auth/unauthorized-continue-uri': 'Unauthorized redirect URL.',
  'auth/user-token-expired': 'Your session has expired. Please sign in again.',
  'auth/invalid-user-token': 'Invalid user token. Please sign in again.',
  'auth/requires-recent-login': 'This operation requires recent authentication. Please sign in again.',
  'auth/credential-already-in-use': 'This credential is already associated with another account.',
  'auth/account-exists-with-different-credential': 'An account already exists with the same email but different sign-in credentials.',
}

export function getAuthErrorMessage(error: any): string {
  if (!error) return 'An unknown error occurred.'
  
  // Handle Firebase Auth errors
  if (error.code && AUTH_ERROR_MESSAGES[error.code]) {
    return AUTH_ERROR_MESSAGES[error.code]
  }
  
  // Handle custom error messages
  if (error.message) {
    return error.message
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error
  }
  
  // Default fallback
  return 'An unexpected error occurred. Please try again.'
}

export function logAuthError(error: any, context?: string) {
  const errorInfo = {
    code: error?.code,
    message: error?.message,
    context,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
  }
  
  console.error('Auth Error:', errorInfo)
  
  // In production, you might want to send this to an error tracking service
  // like Sentry, LogRocket, or similar
}

export function isNetworkError(error: any): boolean {
  return error?.code === 'auth/network-request-failed' || 
         error?.message?.includes('network') ||
         error?.message?.includes('fetch')
}

export function isTemporaryError(error: any): boolean {
  const temporaryErrorCodes = [
    'auth/too-many-requests',
    'auth/network-request-failed',
    'auth/timeout',
  ]
  
  return temporaryErrorCodes.includes(error?.code)
}

export function shouldRetry(error: any, retryCount: number = 0): boolean {
  const maxRetries = 3
  return retryCount < maxRetries && (isNetworkError(error) || isTemporaryError(error))
}

// Rate limiting helper
export class RateLimiter {
  private attempts: Map<string, { count: number; lastAttempt: number }> = new Map()
  private readonly maxAttempts: number
  private readonly windowMs: number

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) { // 15 minutes
    this.maxAttempts = maxAttempts
    this.windowMs = windowMs
  }

  canAttempt(identifier: string): boolean {
    const now = Date.now()
    const record = this.attempts.get(identifier)

    if (!record) {
      return true
    }

    // Reset if window has passed
    if (now - record.lastAttempt > this.windowMs) {
      this.attempts.delete(identifier)
      return true
    }

    return record.count < this.maxAttempts
  }

  recordAttempt(identifier: string): void {
    const now = Date.now()
    const record = this.attempts.get(identifier)

    if (!record || now - record.lastAttempt > this.windowMs) {
      this.attempts.set(identifier, { count: 1, lastAttempt: now })
    } else {
      record.count++
      record.lastAttempt = now
    }
  }

  getRemainingAttempts(identifier: string): number {
    const record = this.attempts.get(identifier)
    if (!record) return this.maxAttempts

    const now = Date.now()
    if (now - record.lastAttempt > this.windowMs) {
      return this.maxAttempts
    }

    return Math.max(0, this.maxAttempts - record.count)
  }

  getTimeUntilReset(identifier: string): number {
    const record = this.attempts.get(identifier)
    if (!record) return 0

    const now = Date.now()
    const timeElapsed = now - record.lastAttempt
    return Math.max(0, this.windowMs - timeElapsed)
  }
}

// Global rate limiter instance
export const authRateLimiter = new RateLimiter()

import { User } from 'firebase/auth'

// Security constants
export const SECURITY_CONFIG = {
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 128,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  ADMIN_ACTIONS_REQUIRE_RECENT_AUTH: 5 * 60 * 1000, // 5 minutes
} as const

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential XSS characters
    .slice(0, 1000) // Limit length
}

export function sanitizeEmail(email: string): string {
  const sanitized = sanitizeInput(email).toLowerCase()
  
  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(sanitized)) {
    throw new Error('Invalid email format')
  }
  
  return sanitized
}

// Password security
export function validatePasswordStrength(password: string): {
  isValid: boolean
  score: number
  feedback: string[]
} {
  const feedback: string[] = []
  let score = 0

  if (password.length < SECURITY_CONFIG.PASSWORD_MIN_LENGTH) {
    feedback.push(`Password must be at least ${SECURITY_CONFIG.PASSWORD_MIN_LENGTH} characters`)
  } else {
    score += 1
  }

  if (password.length > SECURITY_CONFIG.PASSWORD_MAX_LENGTH) {
    feedback.push(`Password must be less than ${SECURITY_CONFIG.PASSWORD_MAX_LENGTH} characters`)
  }

  if (!/[a-z]/.test(password)) {
    feedback.push('Password must contain at least one lowercase letter')
  } else {
    score += 1
  }

  if (!/[A-Z]/.test(password)) {
    feedback.push('Password must contain at least one uppercase letter')
  } else {
    score += 1
  }

  if (!/\d/.test(password)) {
    feedback.push('Password must contain at least one number')
  } else {
    score += 1
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    feedback.push('Password should contain at least one special character')
  } else {
    score += 1
  }

  // Check for common patterns
  if (/(.)\1{2,}/.test(password)) {
    feedback.push('Password should not contain repeated characters')
    score -= 1
  }

  if (/123|abc|qwe|password|admin/i.test(password)) {
    feedback.push('Password should not contain common patterns')
    score -= 1
  }

  return {
    isValid: feedback.length === 0 && score >= 3,
    score: Math.max(0, score),
    feedback
  }
}

// Session management
export function isSessionExpired(lastActivity: number): boolean {
  return Date.now() - lastActivity > SECURITY_CONFIG.SESSION_TIMEOUT
}

export function requiresRecentAuth(lastAuthTime: number): boolean {
  return Date.now() - lastAuthTime > SECURITY_CONFIG.ADMIN_ACTIONS_REQUIRE_RECENT_AUTH
}

// User validation
export function validateUserPermissions(
  user: User | null,
  userRole: { role: string } | null,
  requiredRole?: 'admin' | 'user'
): { isValid: boolean; reason?: string } {
  if (!user) {
    return { isValid: false, reason: 'User not authenticated' }
  }

  if (!userRole) {
    return { isValid: false, reason: 'User role not found' }
  }

  if (requiredRole === 'admin' && userRole.role !== 'admin') {
    return { isValid: false, reason: 'Admin privileges required' }
  }

  return { isValid: true }
}

// Content Security Policy helpers
export function generateNonce(): string {
  const array = new Uint8Array(16)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

// CSRF protection
export function generateCSRFToken(): string {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

export function validateCSRFToken(token: string, expectedToken: string): boolean {
  if (!token || !expectedToken) return false
  
  // Constant-time comparison to prevent timing attacks
  if (token.length !== expectedToken.length) return false
  
  let result = 0
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ expectedToken.charCodeAt(i)
  }
  
  return result === 0
}

// Environment validation
export function validateEnvironment(): {
  isSecure: boolean
  warnings: string[]
} {
  const warnings: string[] = []
  let isSecure = true

  // Check if running in production
  const isProduction = process.env.NODE_ENV === 'production'
  
  // Check HTTPS in production
  if (isProduction && typeof window !== 'undefined') {
    if (window.location.protocol !== 'https:') {
      warnings.push('Application should use HTTPS in production')
      isSecure = false
    }
  }

  // Check for required environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
  ]

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      warnings.push(`Missing required environment variable: ${envVar}`)
      isSecure = false
    }
  }

  return { isSecure, warnings }
}

// Audit logging
export interface SecurityEvent {
  type: 'login' | 'logout' | 'failed_login' | 'role_change' | 'admin_action'
  userId?: string
  email?: string
  ip?: string
  userAgent?: string
  timestamp: string
  details?: Record<string, any>
}

export function logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
  const securityEvent: SecurityEvent = {
    ...event,
    timestamp: new Date().toISOString(),
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Security Event:', securityEvent)
  }

  // In production, you would send this to your security monitoring service
  // Example: sendToSecurityService(securityEvent)
}

// Rate limiting for client-side
export class ClientRateLimiter {
  private attempts: number = 0
  private lastAttempt: number = 0
  private readonly maxAttempts: number
  private readonly windowMs: number

  constructor(maxAttempts: number = 5, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts
    this.windowMs = windowMs
  }

  canAttempt(): boolean {
    const now = Date.now()
    
    if (now - this.lastAttempt > this.windowMs) {
      this.attempts = 0
    }

    return this.attempts < this.maxAttempts
  }

  recordAttempt(): void {
    const now = Date.now()
    
    if (now - this.lastAttempt > this.windowMs) {
      this.attempts = 1
    } else {
      this.attempts++
    }
    
    this.lastAttempt = now
  }

  getRemainingAttempts(): number {
    return Math.max(0, this.maxAttempts - this.attempts)
  }

  getTimeUntilReset(): number {
    const now = Date.now()
    const timeElapsed = now - this.lastAttempt
    return Math.max(0, this.windowMs - timeElapsed)
  }
}

import { collection, addDoc, serverTimestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { ProjectIntakeFormData } from '@/lib/validations/project-intake'

export interface ProjectIntakeSubmission extends ProjectIntakeFormData {
  submittedAt: any // Firestore timestamp
  status: 'new' | 'in-review' | 'contacted' | 'completed'
}

export async function submitProjectIntake(data: ProjectIntakeFormData): Promise<string> {
  console.log('submitProjectIntake called with:', data)
  
  try {
    const submissionData: Omit<ProjectIntakeSubmission, 'id'> = {
      ...data,
      submittedAt: serverTimestamp(),
      status: 'new'
    }

    console.log('Submitting to Firebase:', submissionData)
    const docRef = await addDoc(collection(db, 'project-intakes'), submissionData)
    console.log('Firebase submission successful, doc ID:', docRef.id)
    return docRef.id
  } catch (error) {
    console.error('Error submitting project intake:', error)
    throw new Error('Failed to submit project intake. Please try again.')
  }
} 
import { z } from "zod"

export const projectIntakeSchema = z.object({
  // General Information
  projectName: z.string().min(1, "Project name is required"),
  clientCompanyName: z.string().min(1, "Client/Company name is required"),
  pointOfContact: z.string().min(1, "Point of contact is required"),
  contactEmail: z.string().email("Please enter a valid email address"),
  contactPhone: z.string().min(1, "Phone number is required"),
  dateOfSubmission: z.string().min(1, "Date of submission is required"),
  
  // Platform Details
  productType: z.array(z.string()).min(1, "Please select at least one product type"),
  productTypeOther: z.string().optional(),
  targetPlatforms: z.array(z.string()).min(1, "Please select at least one target platform"),
  isNewProduct: z.enum(["new", "redesign"], {
    required_error: "Please specify if this is a new product or redesign",
  }),
  
  // User & Audience
  estimatedUsers: z.string().min(1, "Estimated number of users is required"),
  targetAudience: z.string().min(10, "Please provide at least 10 characters describing your target audience"),
  userRoles: z.string().optional(),
  
  // Purpose & Goals
  problemSolved: z.string().min(10, "Please provide at least 10 characters describing the problem (minimum 10 characters)"),
  coreFeatures: z.string().min(10, "Please provide at least 10 characters listing core features (minimum 10 characters)"),
  mainGoal: z.string().min(10, "Please provide at least 10 characters describing the main goal (minimum 10 characters)"),
  
  // Technical & Functional Requirements
  requiresAuth: z.boolean(),
  requiresThirdPartyIntegrations: z.boolean(),
  thirdPartyIntegrationsDetails: z.string().optional(),
  hasDesignReferences: z.boolean(),
  designReferencesDetails: z.string().optional(),
  
  // Timeline & Budget (Hidden fields - made optional)
  preferredLaunchDate: z.string().optional(),
  estimatedBudgetRange: z.string().optional(),
  
  // Additional Notes
  specialRequirements: z.string().optional(),
  additionalInfo: z.string().optional(),
})

export type ProjectIntakeFormData = z.infer<typeof projectIntakeSchema> 
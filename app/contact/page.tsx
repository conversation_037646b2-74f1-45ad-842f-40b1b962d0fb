"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Mail, Phone, MapPin, Clock, Brain } from "lucide-react"
import Image from "next/image"

// Metadata is now defined in metadata.ts in the same directory

const contactFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  phone: z.string().optional(),
  company: z.string().optional(),
  industry: z.string().optional(),
  aiInterests: z.string().optional(),
  message: z.string().min(10, { message: "Message must be at least 10 characters." }),
})

type ContactFormValues = z.infer<typeof contactFormSchema>

const contactInfo = [
  {
    icon: <Mail className="h-6 w-6" />,
    title: "Email Us",
    details: "<EMAIL>",
    description: "Our AI specialists are ready to help."
  },
  {
    icon: <Phone className="h-6 w-6" />,
    title: "Call Us",
    details: "+91 91888 78022",
    description: "AI consultation hotline available."
  },
  {
    icon: <MapPin className="h-6 w-6" />,
    title: "Visit Our Studio",
    details: "Cabin 8, Ramanujan Block, Innovation Lab, Kottayam, Kerala - 686532",
    description: ""
  },
  {
    icon: <Brain className="h-6 w-6" />,
    title: "Business Details",
    details: "GSTIN: 32COLPG6895J1ZE",
    description: "Business registration details."
  }
]

export default function ContactPage() {
  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      company: "",
      industry: "",
      aiInterests: "",
      message: "",
    },
  })

  function onSubmit(data: ContactFormValues) {
    // In a real application, you would handle the form submission here
    console.log(data)
    alert("Thank you for your interest in our AI solutions! An AI specialist will be in touch shortly.")
    form.reset()
  }

  return (
    <div className="py-20 md:py-28 lg:py-32 bg-background">
      <div className="container px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-6">Contact Us</Badge>
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
            Request AI Consultation
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto px-4 sm:px-0">
            Discover how our AI-powered solutions can transform your business. Our team of AI specialists
            will work with you to understand your unique challenges and opportunities.
          </p>
        </div>

        {/* Contact Info Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          {contactInfo.map((item) => (
            <div
              key={item.title}
              className="border rounded-lg bg-card p-6 text-center hover:shadow-md transition-shadow"
            >
              <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary mb-4">
                {item.icon}
              </div>
              <h3 className="text-xl font-medium mb-2">{item.title}</h3>
              <p className="font-medium mb-1">{item.details}</p>
              <p className="text-sm text-muted-foreground">{item.description}</p>
            </div>
          ))}
        </div>

        <div className="grid md:grid-cols-5 gap-10 lg:gap-16 items-start">
          <div className="md:col-span-2 space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-4">Explore AI Solutions For Your Business</h2>
              <p className="text-muted-foreground">
                Whether you're looking for custom AI applications, embedded AI hardware, or privacy-focused
                solutions, our team can help guide you through the possibilities and develop a tailored approach
                for your specific industry needs.
              </p>

              <div className="mt-6 space-y-3">
                <div className="flex items-start gap-2">
                  <span className="h-1.5 w-1.5 rounded-full bg-primary mt-2.5" />
                  <p className="text-sm text-muted-foreground">AI readiness assessment for your organization</p>
                </div>
                <div className="flex items-start gap-2">
                  <span className="h-1.5 w-1.5 rounded-full bg-primary mt-2.5" />
                  <p className="text-sm text-muted-foreground">Custom AI solution development roadmap</p>
                </div>
                <div className="flex items-start gap-2">
                  <span className="h-1.5 w-1.5 rounded-full bg-primary mt-2.5" />
                  <p className="text-sm text-muted-foreground">Data privacy and security consultation</p>
                </div>
                <div className="flex items-start gap-2">
                  <span className="h-1.5 w-1.5 rounded-full bg-primary mt-2.5" />
                  <p className="text-sm text-muted-foreground">ROI projection for AI implementation</p>
                </div>
              </div>
            </div>

            <div className="rounded-lg overflow-hidden h-64 md:h-80 bg-muted relative shadow-sm">
              <Image
                src="https://images.pexels.com/photos/2085831/pexels-photo-2085831.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="AI Demo Visualization showing neural network patterns"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
          </div>

          {/* Contact Form */}
          <div className="md:col-span-3 border rounded-xl p-6 md:p-8 lg:p-10 bg-card shadow-sm">
            <h3 className="text-xl font-semibold mb-6">Request AI Consultation</h3>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="John Doe" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="+****************" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="company"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your Company" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="industry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Industry</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. Healthcare, Finance, Manufacturing" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="aiInterests"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>AI Interests</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. Machine Learning, Privacy AI" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Details</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your business challenges and how you think AI could help..."
                          className="min-h-[120px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="pt-2">
                  <Button type="submit" className="w-full md:w-auto" size="lg">
                    Submit
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  )
}
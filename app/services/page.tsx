import { Badge } from "@/components/ui/badge"
import { Brain, Cpu, Shield, Code, Layers, Database, Bot, ChartBar } from "lucide-react"
import Image from "next/image"

export const metadata = {
  title: "AI Solutions - Agnex Studio",
  description: "Explore our range of AI-powered solutions including custom applications, privacy-focused AI, and specialized solutions for your business.",
}

const services = [
  {
    id: "ai-applications",
    icon: <Brain className="h-8 w-8" />,
    title: "AI-Driven Applications",
    description: "Next-generation applications powered by advanced AI technologies that deliver smarter, faster, and more intuitive digital experiences.",
    features: [
      "Intelligent web and mobile applications",
      "AI-powered recommendation systems",
      "Natural language processing interfaces",
      "Computer vision solutions",
      "Voice and speech recognition systems",
      "Intelligent document processing"
    ],
    image: "https://images.pexels.com/photos/27207384/pexels-photo-27207384/free-photo-of-the-steering-wheel-and-driver-s-seat-view-of-the-kia-ev6-gt-line.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: "embedded-ai",
    icon: <Cpu className="h-8 w-8" />,
    title: "Embedded AI Solutions",
    description: "Specialized hardware solutions with onboard AI processing for sectors requiring the highest levels of data privacy and security.",
    features: [
      "On-device AI processing",
      "Custom AI hardware integration",
      "Edge AI deployment",
      "Real-time decision making",
      "Reduced cloud dependency",
      "Lower operational costs"
    ],
    image: "https://images.pexels.com/photos/3520665/pexels-photo-3520665.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: "privacy-ai",
    icon: <Shield className="h-8 w-8" />,
    title: "Privacy-Focused AI",
    description: "AI solutions with local processing capabilities that ensure sensitive data remains secure and confidential within your organization.",
    features: [
      "GDPR and HIPAA compliant AI systems",
      "Federated learning implementation",
      "Secure multi-party computation",
      "Differential privacy techniques",
      "Encrypted AI processing",
      "Data sovereignty compliance"
    ],
    image: "https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: "custom-ai",
    icon: <Code className="h-8 w-8" />,
    title: "Custom AI Development",
    description: "Tailored AI applications and algorithms designed to meet your specific business challenges and operational requirements.",
    features: [
      "Custom machine learning models",
      "AI algorithm development",
      "Business process automation",
      "Workflow optimization",
      "AI integration with legacy systems",
      "Continuous improvement frameworks"
    ],
    image: "https://images.pexels.com/photos/2582932/pexels-photo-2582932.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: "smart-automation",
    icon: <Layers className="h-8 w-8" />,
    title: "Smart Automation",
    description: "Intelligent automation solutions that increase efficiency, reduce costs, and unlock new growth opportunities for your business.",
    features: [
      "Robotic Process Automation (RPA)",
      "AI-enhanced workflow automation",
      "Intelligent document processing",
      "Smart customer service solutions",
      "Predictive maintenance systems",
      "Supply chain optimization"
    ],
    image: "https://images.pexels.com/photos/2599244/pexels-photo-2599244.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: "ml-integration",
    icon: <ChartBar className="h-8 w-8" />,
    title: "Machine Learning Integration",
    description: "Seamlessly integrate machine learning algorithms into your existing systems for enhanced data analysis and predictive capabilities.",
    features: [
      "Predictive analytics implementation",
      "Deep learning integration",
      "Big data processing frameworks",
      "Time series forecasting",
      "Anomaly detection systems",
      "Decision support systems"
    ],
    image: "https://images.pexels.com/photos/669615/pexels-photo-669615.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
]

export default function ServicesPage() {
  return (
    <div className="py-12 md:py-20 bg-background">
      <div className="container">
        <div className="text-center mb-16">
          <Badge className="mb-4">Our AI Solutions</Badge>
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4">
            AI-Powered Innovation for Your Business
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            We create next-generation AI solutions that transform how businesses operate,
            delivering smarter, faster, and more intuitive experiences while prioritizing
            data privacy and security.
          </p>
        </div>

        <div className="space-y-32">
          {services.map((service, index) => (
            <div
              key={service.id}
              id={service.id}
              className={`grid md:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'md:flex-row-reverse' : ''
              }`}
            >
              <div>
                <div className="inline-flex items-center justify-center h-16 w-16 rounded-lg bg-primary/10 mb-6">
                  {service.icon}
                </div>
                <h2 className="text-3xl font-bold mb-4">{service.title}</h2>
                <p className="text-lg text-muted-foreground mb-8">
                  {service.description}
                </p>

                <div className="border-t border-border pt-6">
                  <h3 className="text-xl font-medium mb-4">Capabilities & Benefits</h3>
                  <ul className="grid grid-cols-1 sm:grid-cols-2 gap-y-3 gap-x-6">
                    {service.features.map((feature) => (
                      <li key={feature} className="flex items-center gap-2">
                        <span className="h-1.5 w-1.5 rounded-full bg-primary" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="relative h-72 md:h-96 bg-muted rounded-lg overflow-hidden shadow-md">
                <Image
                  src={service.image}
                  alt={`${service.title} illustration`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
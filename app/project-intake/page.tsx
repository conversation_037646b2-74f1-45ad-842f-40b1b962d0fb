import { Metadata } from "next"
import { ProjectIntakeForm } from "@/components/project-intake/project-intake-form"

export const metadata: Metadata = {
  title: "Project Consultation | AgneX Studios",
  description: "Tell us about your project vision and requirements. Our team will prepare a detailed proposal tailored to your needs.",
}

export default function ProjectIntakePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-blue-50/30 dark:to-blue-950/30">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Project Consultation
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Share your project vision with us. We'll transform your ideas into innovative digital solutions 
            that help your business thrive in the modern world.
          </p>
          <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              24-hour response time
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Free consultation
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              Detailed proposal
            </div>
          </div>
        </div>

        {/* Form Section */}
        <ProjectIntakeForm />
      </div>
    </div>
  )
} 
import Image from "next/image"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { ArrowRight } from "lucide-react"

export const metadata = {
  title: "AI Solutions Portfolio - Agnex Studio",
  description: "Explore our showcase of successful AI projects that have transformed businesses across industries",
}

const categories = ["All", "AI Applications", "Embedded AI", "Privacy AI", "Smart Automation", "Machine Learning"]

const projects = [
  {
    id: "ai-healthcare-platform",
    title: "AI-Powered Healthcare Diagnostics",
    category: "AI Applications",
    image: "/images/portfolio-1.jpg",
    description: "Privacy-preserving AI diagnostic system that processes patient data locally, improving diagnostic accuracy by 87% while maintaining full HIPAA compliance.",
    clientName: "National Health Network"
  },
  {
    id: "financial-prediction",
    title: "Financial Risk Assessment AI",
    category: "Machine Learning",
    image: "/images/portfolio-2.jpg",
    description: "Machine learning system for real-time risk assessment that increased fraud detection by 93% and reduced false positives by 78%.",
    clientName: "Global Banking Corporation"
  },
  {
    id: "retail-automation",
    title: "Retail Smart Automation Platform",
    category: "Smart Automation",
    image: "/images/portfolio-3.jpg",
    description: "End-to-end AI automation solution that optimized inventory management, reduced waste by 42%, and increased sales through predictive stocking.",
    clientName: "Retail Chain Group"
  },
  {
    id: "embedded-manufacturing",
    title: "Embedded AI for Manufacturing",
    category: "Embedded AI",
    image: "/images/portfolio-4.jpg",
    description: "On-device AI quality control system that processes inspection data locally, reducing defect rates by 63% and improving production efficiency by 35%.",
    clientName: "Advanced Manufacturing Inc."
  },
  {
    id: "secure-government-ai",
    title: "Secure AI Data Processing",
    category: "Privacy AI",
    image: "/images/portfolio-5.jpg",
    description: "Ultra-secure AI data processing system with hardware-level encryption that enables advanced analytics while meeting the highest security standards.",
    clientName: "Government Security Agency"
  },
  {
    id: "predictive-maintenance",
    title: "AI Predictive Maintenance System",
    category: "Machine Learning",
    image: "/images/portfolio-6.jpg",
    description: "Machine learning system that predicts equipment failures with 96% accuracy, reducing downtime by 83% and maintenance costs by 47%.",
    clientName: "Energy Infrastructure Corp"
  }
]

export default function PortfolioPage() {
  return (
    <div className="py-20 md:py-28 lg:py-32 bg-background">
      <div className="container px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-6">AI Solutions Portfolio</Badge>
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
            AI Transformation in Action
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto px-4 sm:px-0">
            Explore our showcase of successful AI implementations that have helped businesses across industries
            increase efficiency, enhance security, and unlock new growth opportunities.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-2 mb-16">
          {categories.map((category) => (
            <Button
              key={category}
              variant={category === "All" ? "default" : "outline"}
              className="rounded-full mb-2"
            >
              {category}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <Card key={project.id} className="overflow-hidden flex flex-col h-full hover:shadow-lg transition-shadow">
              <div className="relative aspect-[16/9] w-full">
                <Image
                  src={project.image}
                  alt={project.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
              <CardContent className="pt-6 flex-grow">
                <div className="flex flex-wrap items-center justify-between gap-2 mb-3">
                  <Badge variant="secondary">{project.category}</Badge>
                  <span className="text-sm text-muted-foreground">{project.clientName}</span>
                </div>
                <h3 className="text-xl font-bold mb-3 line-clamp-2">{project.title}</h3>
                <p className="text-muted-foreground line-clamp-3">{project.description}</p>
              </CardContent>
              <CardFooter className="pt-2 pb-4">
                <Button asChild variant="ghost" className="w-full gap-1 justify-center">
                  <Link href={`/portfolio/${project.id}`}>
                    View Case Study <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { ThemeProvider } from "@/components/theme/theme-provider";
import { ThreeDiagnostics } from "@/components/debug/three-diagnostics";
import { cn } from "@/lib/utils";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Agnex Studio",
  description: "We create innovative digital solutions that help businesses thrive in the modern world through AI-powered technology.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <head>
        <script dangerouslySetInnerHTML={{
          __html: `
            (function() {
              try {
                const storageKey = "agnexstudio-ui-theme";
                let savedTheme;

                try {
                  savedTheme = localStorage.getItem(storageKey);
                } catch (storageError) {
                  // Fallback if localStorage is not available
                  console.warn("localStorage not available:", storageError);
                }

                const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
                const theme = savedTheme || "system";
                const resolvedTheme = theme === "system" ? systemTheme : theme;

                document.documentElement.classList.remove("light", "dark");
                document.documentElement.classList.add(resolvedTheme);

                // Store the resolved theme for components that need to know the actual theme
                try {
                  localStorage.setItem(storageKey + "-resolved", resolvedTheme);
                } catch (storageError) {
                  // Ignore localStorage errors
                }
              } catch (e) {
                console.error("Theme initialization error:", e);
                // Fallback to light theme if there's an error
                document.documentElement.classList.remove("light", "dark");
                document.documentElement.classList.add("light");
              }
            })();
          `,
        }} />
      </head>
      <body className={cn(inter.className, "antialiased min-h-screen")} suppressHydrationWarning>
        <ThemeProvider
          defaultTheme="dark"
          storageKey="agnexstudio-ui-theme"
        >
          <div className="flex flex-col min-h-screen">
            <Navbar />
            <main className="flex-1 w-full mx-auto">
              {children}
            </main>
            <Footer />
            <ThreeDiagnostics />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}

import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Users, Calendar, Brain } from "lucide-react"

export const metadata = {
  title: "About Agnex Studio - Leading the Future With AI",
  description: "Learn about Agnex Studio, a forward-thinking company at the cutting edge of Artificial Intelligence technology.",
}



const values = [
  {
    name: "AI-Driven Innovation",
    description: "We constantly push the boundaries of what AI can do, creating solutions that transform businesses."
  },
  {
    name: "Privacy & Security",
    description: "We prioritize data confidentiality and security in all our AI implementations, especially for sensitive industries."
  },
  {
    name: "Custom Intelligence",
    description: "We develop bespoke AI solutions tailored to each client's unique challenges and opportunities."
  },
  {
    name: "Ethical AI",
    description: "We develop responsible AI systems that are fair, transparent, and aligned with human values."
  },
  {
    name: "Future-Ready",
    description: "We anticipate tomorrow's challenges with AI solutions that evolve and adapt over time."
  },
  {
    name: "Measurable Impact",
    description: "We create AI solutions that deliver quantifiable business value and ROI for our clients."
  },
]



export default function AboutPage() {
  return (
    <div className="bg-background">
      {/* Hero Section */}
      <section className="py-16 md:py-24 border-b">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4">About Agnex Studio</Badge>
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
                Leading the Future with AI-Powered Innovation
              </h1>
              <p className="text-lg text-muted-foreground">
                Agnex Studio is a forward-thinking company at the cutting edge of Artificial Intelligence technology.
                We create custom AI-driven applications that transform how businesses operate. Our approach combines
                AI innovation with business understanding, helping organizations meet today's challenges and prepare for tomorrow.
              </p>
            </div>
            <div className="relative h-72 md:h-96 bg-muted rounded-lg overflow-hidden shadow-md">
              <Image
                src="https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Agnex Studio team collaborating on innovative solutions"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 md:py-24 border-b">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <Badge className="mb-4">Our Story</Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-6">
              From AI Visionaries to Industry Leaders
            </h2>
          </div>
          <div className="space-y-8">
            <p className="text-lg">
              Agnex Studio was founded with a clear vision: to make advanced AI technologies accessible to businesses of all sizes.
              We recognized that while AI has tremendous potential, many organizations lack the expertise to implement it effectively.
            </p>
            <p className="text-lg">
              Starting with a small team of AI specialists, we focused on developing custom solutions that solve real business problems.
              Our success in creating intelligent applications that deliver measurable ROI quickly established our reputation.
            </p>
            <p className="text-lg">
              Today, Agnex Studio is recognized for AI-driven innovation. Our team works with organizations worldwide to create
              custom AI solutions that increase efficiency, reduce costs, and unlock new growth opportunities – all while maintaining
              the highest standards of data privacy and security.
            </p>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-16 md:py-24 border-b">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <Badge className="mb-4">Our Values</Badge>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-6">
              The Principles Behind Our AI
            </h2>
            <p className="text-lg text-muted-foreground">
              These core values guide our approach to AI development and define our company culture.
            </p>
          </div>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {values.map((value) => (
              <div key={value.name} className="p-6 rounded-lg border bg-card">
                <h3 className="text-xl font-semibold mb-2">{value.name}</h3>
                <p className="text-muted-foreground">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>


    </div>
  )
}
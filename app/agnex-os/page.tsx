"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import {
  Globe,
  Cpu,
  Shield,
  Briefcase,
  GraduationCap,
  Command,
  Cloud,
  Layers,
  Zap,
  Brain,
  Users,
  Bot,
  Sparkles,
  Workflow,
  LayoutDashboard,
  MessageSquare,
  FileText,
  Calendar,
  LineChart,
  MousePointer,
  Fingerprint,
  Lightbulb,
  Bell,
  ArrowUp,
  Trophy
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"

// Metadata is now in a separate file

const features = [
  {
    icon: <Globe className="h-6 w-6" />,
    title: "Browser-First Architecture",
    description: "Access your complete workspace from any device with a modern browser, no installation required."
  },
  {
    icon: <Brain className="h-6 w-6" />,
    title: "AI-Native Interface",
    description: "Every interaction is enhanced with contextual AI that learns your preferences and workflow patterns."
  },
  {
    icon: <Command className="h-6 w-6" />,
    title: "Universal Command Bar",
    description: "Control your entire workspace with natural language commands and AI-powered suggestions."
  },
  {
    icon: <Cloud className="h-6 w-6" />,
    title: "Seamless Cloud Integration",
    description: "Your data syncs automatically across all devices with enterprise-grade security and encryption."
  },
  {
    icon: <Layers className="h-6 w-6" />,
    title: "Modular App Ecosystem",
    description: "Customize your workspace with a growing library of specialized productivity applications."
  },
  {
    icon: <Zap className="h-6 w-6" />,
    title: "Offline Capabilities",
    description: "Continue working without interruption even when your internet connection is unstable or unavailable."
  },
]

const enterpriseFeatures = [
  {
    icon: <LayoutDashboard className="h-8 w-8" />,
    title: "Unified Workspace",
    description: "Replace fragmented SaaS tools with a cohesive environment that brings all your business applications into one intelligent interface."
  },
  {
    icon: <Briefcase className="h-8 w-8" />,
    title: "Modular CRM Core",
    description: "Comprehensive customer relationship management with sales pipeline tracking, customer profiles, ticketing, and intelligent automation."
  },
  {
    icon: <MessageSquare className="h-8 w-8" />,
    title: "Integrated Communication",
    description: "Email, messaging, video conferencing, and team collaboration tools seamlessly integrated into your workspace."
  },
  {
    icon: <FileText className="h-8 w-8" />,
    title: "Document Intelligence",
    description: "AI-powered document creation, editing, and management with smart templates and automated workflows."
  },
  {
    icon: <Calendar className="h-8 w-8" />,
    title: "Smart Scheduling",
    description: "AI calendar that optimizes your time, suggests meeting slots, and automates scheduling based on priorities."
  },
  {
    icon: <LineChart className="h-8 w-8" />,
    title: "Business Analytics",
    description: "Real-time dashboards and reports with AI-generated insights and recommendations for business optimization."
  },
]

const educationFeatures = [
  {
    icon: <GraduationCap className="h-8 w-8" />,
    title: "Learning Dashboard",
    description: "Centralized hub for courses, assignments, resources, and progress tracking with personalized learning paths."
  },
  {
    icon: <Bot className="h-8 w-8" />,
    title: "AI Learning Companion",
    description: "Personalized tutoring, concept explanations, and study assistance powered by advanced language models."
  },
  {
    icon: <Users className="h-8 w-8" />,
    title: "Collaborative Learning",
    description: "Virtual classrooms, group projects, and peer feedback systems designed for effective remote collaboration."
  },
  {
    icon: <Sparkles className="h-8 w-8" />,
    title: "Gamified Experience",
    description: "Achievement systems, progress visualization, and reward mechanisms that boost engagement and motivation."
  },
  {
    icon: <Workflow className="h-8 w-8" />,
    title: "Adaptive Learning Paths",
    description: "AI-generated personalized learning journeys that adapt to your progress, strengths, and areas for improvement."
  },
  {
    icon: <Shield className="h-8 w-8" />,
    title: "Privacy-First Design",
    description: "Student data protection with configurable privacy controls and compliance with educational data regulations."
  },
]

export default function AgneXOSPage() {
  return (
    <div className="py-12 md:py-20 bg-background">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-radial from-primary/5 via-background to-background"></div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/3 right-1/3 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="container relative z-10 mb-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto pt-16 pb-8"
          >
            <Badge className="mb-4" variant="outline">
              <span className="flex items-center gap-1">
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-primary"></span>
                </span>
                Coming Soon
              </span>
            </Badge>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary via-blue-500 to-purple-500">
              AgneX OS
            </h1>
            <p className="text-2xl md:text-3xl font-medium mb-6">
              The AI-Powered WebOS for Work & Learning
            </p>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              A next-generation operating environment that runs seamlessly in your browser or as a desktop application,
              providing an AI-enhanced workspace tailored for productivity and education.
            </p>
            <div className="flex flex-wrap justify-center gap-4 pt-4">
              <Button size="lg" className="rounded-full px-8 py-6 text-lg shadow-lg shadow-primary/20 hover:shadow-primary/30 transition-all duration-300" asChild>
                <Link href="#enterprise">Enterprise Edition</Link>
              </Button>
              <Button size="lg" variant="outline" className="rounded-full px-8 py-6 text-lg backdrop-blur-sm bg-background/50 border-primary/20 hover:bg-background/70 transition-all duration-300" asChild>
                <Link href="#education">Education Edition</Link>
              </Button>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative mt-16 mx-auto max-w-5xl"
          >
            <div className="relative h-[500px] md:h-[600px] rounded-2xl overflow-hidden shadow-2xl border border-white/10 backdrop-blur-sm">
              {/* Futuristic gradient background with subtle visual elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-cyan-950 to-slate-950">
                {/* Animated grid pattern */}
                <div className="absolute inset-0 opacity-20"
                     style={{
                       backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
                       backgroundSize: '40px 40px',
                       animation: 'moveGrid 20s linear infinite'
                     }}>
                </div>

                {/* Glowing orbs with next-gen colors */}
                <div className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-cyan-500/20 blur-xl animate-pulse"></div>
                <div className="absolute bottom-1/3 right-1/3 w-40 h-40 rounded-full bg-emerald-500/20 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
                <div className="absolute top-2/3 left-1/2 w-24 h-24 rounded-full bg-teal-500/20 blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

                {/* Additional gradient elements */}
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-cyan-500/5 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 right-0 w-full h-full bg-gradient-radial from-emerald-500/5 via-transparent to-transparent"></div>

                {/* Subtle digital noise texture */}
                <div className="absolute inset-0 opacity-5 mix-blend-overlay"
                     style={{
                       backgroundImage: 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 200 200\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.65\' numOctaves=\'3\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E")',
                       backgroundSize: '200px 200px'
                     }}>
                </div>
              </div>

              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-background via-background/20 to-transparent"></div>

              {/* Floating UI elements */}
              <div className="absolute top-8 left-8 right-8 flex justify-between items-center">
                <div className="bg-background/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-red-500"></div>
                    <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                    <div className="h-3 w-3 rounded-full bg-green-500"></div>
                    <span className="ml-2 text-white font-medium">AgneX OS</span>
                  </div>
                </div>
                <div className="bg-background/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10 shadow-lg">
                  <div className="flex items-center gap-3">
                    <Fingerprint className="h-5 w-5 text-blue-400" />
                    <span className="text-white font-medium">AI Assistant</span>
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                  </div>
                </div>
              </div>

              {/* AI Assistant popup */}
              <div className="absolute bottom-8 right-8 max-w-xs bg-background/40 backdrop-blur-xl rounded-2xl p-4 border border-white/10 shadow-lg">
                <div className="flex items-start gap-3">
                  <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white font-medium">AgneX Assistant</p>
                    <p className="text-sm text-white/80">I've analyzed your schedule. Would you like me to prepare the presentation for your 2PM meeting?</p>
                  </div>
                </div>
                <div className="flex gap-2 mt-3">
                  <Button size="sm" className="rounded-full text-xs bg-blue-500 hover:bg-blue-600">Yes, please</Button>
                  <Button size="sm" variant="outline" className="rounded-full text-xs bg-transparent border-white/20 text-white hover:bg-white/10">Later</Button>
                </div>
              </div>

              {/* Command bar */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[80%] max-w-2xl">
                <div className="bg-background/30 backdrop-blur-xl rounded-2xl p-4 border border-white/10 shadow-xl">
                  <div className="flex items-center gap-3">
                    <Command className="h-6 w-6 text-blue-400" />
                    <input type="text" className="bg-transparent border-none outline-none text-white w-full" placeholder="Ask AgneX OS anything..." disabled />
                    <MousePointer className="h-5 w-5 text-white/60" />
                  </div>
                  <div className="mt-3 pt-3 border-t border-white/10">
                    <p className="text-sm text-white/80 mb-2">Suggested actions:</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-blue-500/20 text-blue-200 text-xs rounded-full px-3 py-1">Schedule meeting</span>
                      <span className="bg-purple-500/20 text-purple-200 text-xs rounded-full px-3 py-1">Analyze sales data</span>
                      <span className="bg-green-500/20 text-green-200 text-xs rounded-full px-3 py-1">Draft email</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating badges */}
            <div className="absolute -bottom-5 -left-5 bg-background/80 backdrop-blur-md rounded-full px-4 py-2 shadow-lg border border-primary/20">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary" />
                <span className="font-medium">AI-Powered Interface</span>
              </div>
            </div>
            <div className="absolute -bottom-5 right-24 bg-background/80 backdrop-blur-md rounded-full px-4 py-2 shadow-lg border border-blue-500/20">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Seamless Experience</span>
              </div>
            </div>
            <div className="absolute -bottom-5 -right-5 bg-background/80 backdrop-blur-md rounded-full px-4 py-2 shadow-lg border border-purple-500/20">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-purple-500" />
                <span className="font-medium">Smart Automation</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Core Features */}
      <div className="container mb-24 relative">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-20 -right-20 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-20 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16 relative"
        >
          <Badge className="mb-4" variant="outline">Core Features</Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            The Next Evolution in Digital Workspaces
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
            AgneX OS combines the accessibility of web applications with the power and integration of desktop environments,
            all enhanced by contextual AI assistance that learns and adapts to your workflow.
          </p>
        </motion.div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 relative">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-blue-500/5 rounded-2xl transform group-hover:scale-105 transition-all duration-300 opacity-0 group-hover:opacity-100"></div>
              <div className="relative p-8 rounded-2xl border border-border/30 bg-card/30 backdrop-blur-sm hover:shadow-xl transition-all duration-300 h-full">
                <div className="absolute -top-5 -right-5 w-24 h-24 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="inline-flex items-center justify-center h-14 w-14 rounded-2xl bg-gradient-to-br from-primary/10 to-blue-500/10 border border-white/5 mb-6 shadow-inner">
                  <div className="text-primary group-hover:text-blue-500 transition-colors duration-300">
                    {feature.icon}
                  </div>
                </div>

                <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors duration-300">{feature.title}</h3>
                <p className="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Decorative element */}
        <div className="mt-16 flex justify-center">
          <div className="h-px w-24 bg-gradient-to-r from-transparent via-border to-transparent"></div>
        </div>
      </div>

      {/* Enterprise Edition */}
      <div id="enterprise" className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-background via-blue-950/10 to-background"></div>
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 left-1/4 w-[800px] h-[800px] bg-blue-500/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-[600px] h-[600px] bg-indigo-500/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="text-center mb-20"
          >
            <Badge className="mb-4" variant="secondary">Enterprise Edition</Badge>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600">
              AgneX OS for Enterprise
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
              A fully integrated, AI-powered workspace for businesses that replaces fragmented SaaS tools
              with a cohesive, browser-based desktop environment designed for maximum productivity.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-16 items-center mb-24">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl blur opacity-20"></div>
              <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl border border-white/10 backdrop-blur-sm">
                {/* Next-gen Enterprise gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-sky-950 to-slate-950">
                  {/* Animated grid pattern */}
                  <div className="absolute inset-0 opacity-20"
                       style={{
                         backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
                         backgroundSize: '40px 40px',
                         animation: 'moveGrid 20s linear infinite'
                       }}>
                  </div>

                  {/* Glowing orbs with modern colors */}
                  <div className="absolute top-1/3 left-1/3 w-40 h-40 rounded-full bg-sky-500/20 blur-xl animate-pulse"></div>
                  <div className="absolute bottom-1/4 right-1/4 w-32 h-32 rounded-full bg-teal-500/20 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
                  <div className="absolute top-2/3 left-1/2 w-24 h-24 rounded-full bg-cyan-500/20 blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

                  {/* Additional gradient elements */}
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-sky-500/5 via-transparent to-transparent"></div>

                  {/* Subtle digital noise texture */}
                  <div className="absolute inset-0 opacity-5 mix-blend-overlay"
                       style={{
                         backgroundImage: 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 200 200\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.65\' numOctaves=\'3\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E")',
                         backgroundSize: '200px 200px'
                       }}>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-tr from-slate-950/80 to-transparent"></div>

                {/* UI Elements overlay */}
                <div className="absolute inset-0 flex flex-col p-6">
                  {/* Top bar */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                        <span className="text-white text-sm font-medium">AgneX Enterprise</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div className="h-8 w-8 rounded-full bg-black/30 backdrop-blur-md flex items-center justify-center border border-white/10">
                        <Briefcase className="h-4 w-4 text-blue-400" />
                      </div>
                      <div className="h-8 w-8 rounded-full bg-black/30 backdrop-blur-md flex items-center justify-center border border-white/10">
                        <MessageSquare className="h-4 w-4 text-blue-400" />
                      </div>
                      <div className="h-8 w-8 rounded-full bg-black/30 backdrop-blur-md flex items-center justify-center border border-white/10">
                        <Bell className="h-4 w-4 text-blue-400" />
                      </div>
                    </div>
                  </div>

                  {/* Dashboard widgets */}
                  <div className="grid grid-cols-2 gap-4 mb-auto">
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-white text-sm font-medium">Sales Pipeline</h4>
                        <LineChart className="h-4 w-4 text-blue-400" />
                      </div>
                      <div className="h-24 flex items-end gap-1">
                        <div className="h-[30%] w-full bg-blue-500/30 rounded-t-sm"></div>
                        <div className="h-[50%] w-full bg-blue-500/50 rounded-t-sm"></div>
                        <div className="h-[80%] w-full bg-blue-500/70 rounded-t-sm"></div>
                        <div className="h-[60%] w-full bg-blue-500/60 rounded-t-sm"></div>
                        <div className="h-[40%] w-full bg-blue-500/40 rounded-t-sm"></div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-white text-sm font-medium">Team Activity</h4>
                        <Users className="h-4 w-4 text-blue-400" />
                      </div>
                      <div className="flex gap-2 mt-4">
                        <div className="h-8 w-8 rounded-full bg-blue-500/20 flex items-center justify-center text-xs text-white">JD</div>
                        <div className="h-8 w-8 rounded-full bg-purple-500/20 flex items-center justify-center text-xs text-white">KL</div>
                        <div className="h-8 w-8 rounded-full bg-green-500/20 flex items-center justify-center text-xs text-white">MN</div>
                        <div className="h-8 w-8 rounded-full bg-yellow-500/20 flex items-center justify-center text-xs text-white">+5</div>
                      </div>
                    </div>
                  </div>

                  {/* AI Assistant */}
                  <div className="bg-black/40 backdrop-blur-xl rounded-xl p-4 border border-white/10 mt-4">
                    <div className="flex items-start gap-3">
                      <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <Sparkles className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-white text-sm font-medium">Enterprise Assistant</p>
                        <p className="text-xs text-white/80">I've analyzed the Q3 sales data. Revenue is up 24% from last quarter. Would you like me to prepare a report?</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              {enterpriseFeatures.slice(0, 3).map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group flex gap-6 p-4 rounded-xl hover:bg-blue-950/10 transition-colors duration-300"
                >
                  <div className="flex-shrink-0 inline-flex items-center justify-center h-14 w-14 rounded-xl bg-gradient-to-br from-blue-500/10 to-indigo-500/10 border border-white/5 shadow-inner">
                    <div className="text-blue-500 group-hover:text-blue-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-blue-400 transition-colors duration-300">{feature.title}</h3>
                    <p className="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          <div className="grid md:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="space-y-8 order-2 md:order-1"
            >
              {enterpriseFeatures.slice(3).map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group flex gap-6 p-4 rounded-xl hover:bg-blue-950/10 transition-colors duration-300"
                >
                  <div className="flex-shrink-0 inline-flex items-center justify-center h-14 w-14 rounded-xl bg-gradient-to-br from-blue-500/10 to-indigo-500/10 border border-white/5 shadow-inner">
                    <div className="text-blue-500 group-hover:text-blue-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-blue-400 transition-colors duration-300">{feature.title}</h3>
                    <p className="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="relative order-1 md:order-2"
            >
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl blur opacity-20"></div>
              <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl border border-white/10 backdrop-blur-sm">
                {/* Next-gen Enterprise Dashboard gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-cyan-950 to-slate-950">
                  {/* Animated grid pattern */}
                  <div className="absolute inset-0 opacity-20"
                       style={{
                         backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
                         backgroundSize: '40px 40px',
                         animation: 'moveGrid 20s linear infinite'
                       }}>
                  </div>

                  {/* Glowing orbs with modern colors */}
                  <div className="absolute top-1/4 right-1/4 w-40 h-40 rounded-full bg-cyan-500/20 blur-xl animate-pulse"></div>
                  <div className="absolute bottom-1/3 left-1/3 w-32 h-32 rounded-full bg-emerald-500/20 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
                  <div className="absolute top-1/2 right-1/2 w-24 h-24 rounded-full bg-teal-500/20 blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

                  {/* Additional gradient elements */}
                  <div className="absolute bottom-0 right-0 w-full h-full bg-gradient-radial from-cyan-500/5 via-transparent to-transparent"></div>

                  {/* Subtle digital noise texture */}
                  <div className="absolute inset-0 opacity-5 mix-blend-overlay"
                       style={{
                         backgroundImage: 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 200 200\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.65\' numOctaves=\'3\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E")',
                         backgroundSize: '200px 200px'
                       }}>
                  </div>

                  {/* Subtle circuit-like pattern */}
                  <div className="absolute inset-0 opacity-10"
                       style={{
                         backgroundImage: 'radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.2) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(255, 255, 255, 0.2) 1px, transparent 0)',
                         backgroundSize: '100px 100px'
                       }}>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent to-slate-950/80"></div>

                {/* Analytics Dashboard overlay */}
                <div className="absolute inset-0 flex flex-col p-6">
                  {/* Top bar */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                      <div className="flex items-center gap-2">
                        <LineChart className="h-4 w-4 text-blue-400" />
                        <span className="text-white text-sm font-medium">Analytics Dashboard</span>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-blue-400" />
                        <span className="text-white text-sm font-medium">Q3 2024</span>
                      </div>
                    </div>
                  </div>

                  {/* Main chart */}
                  <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10 mb-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-white text-sm font-medium">Revenue Growth</h4>
                      <div className="flex gap-2">
                        <div className="px-2 py-1 rounded-md bg-blue-500/20 text-xs text-blue-200">Daily</div>
                        <div className="px-2 py-1 rounded-md bg-black/20 text-xs text-white/80">Weekly</div>
                        <div className="px-2 py-1 rounded-md bg-black/20 text-xs text-white/80">Monthly</div>
                      </div>
                    </div>
                    <div className="h-40 flex items-end gap-1 pt-4">
                      <div className="relative h-full w-full">
                        {/* Line chart */}
                        <svg className="w-full h-full" viewBox="0 0 100 40">
                          <path d="M0,35 Q10,20 20,25 T40,15 T60,20 T80,5 T100,15" fill="none" stroke="rgba(59, 130, 246, 0.5)" strokeWidth="0.5" />
                          <path d="M0,35 Q10,20 20,25 T40,15 T60,20 T80,5 T100,15" fill="url(#blueGradient)" fillOpacity="0.2" />
                          <defs>
                            <linearGradient id="blueGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                              <stop offset="0%" stopColor="rgba(59, 130, 246, 0.5)" />
                              <stop offset="100%" stopColor="rgba(59, 130, 246, 0)" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Bottom widgets */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex flex-col items-center">
                        <div className="text-xs text-white/80 mb-1">Customers</div>
                        <div className="text-xl font-bold text-white">2,458</div>
                        <div className="text-xs text-green-400 flex items-center gap-1 mt-1">
                          <ArrowUp className="h-3 w-3" />
                          <span>12%</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex flex-col items-center">
                        <div className="text-xs text-white/80 mb-1">Revenue</div>
                        <div className="text-xl font-bold text-white">$842K</div>
                        <div className="text-xs text-green-400 flex items-center gap-1 mt-1">
                          <ArrowUp className="h-3 w-3" />
                          <span>24%</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex flex-col items-center">
                        <div className="text-xs text-white/80 mb-1">Conversion</div>
                        <div className="text-xl font-bold text-white">8.9%</div>
                        <div className="text-xs text-green-400 flex items-center gap-1 mt-1">
                          <ArrowUp className="h-3 w-3" />
                          <span>3.2%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Education Edition */}
      <div id="education" className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-background via-purple-950/10 to-background"></div>
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 right-1/4 w-[800px] h-[800px] bg-purple-500/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-1/4 w-[600px] h-[600px] bg-pink-500/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="text-center mb-20"
          >
            <Badge className="mb-4" variant="outline">Education Edition</Badge>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500">
              AgneX OS for Education
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
              A personalized, immersive learning environment that mimics a full OS, where students can manage courses,
              content, and communication through one AI-assisted interface that adapts to individual learning styles.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-16 items-center mb-24">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl blur opacity-20"></div>
              <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl border border-white/10 backdrop-blur-sm">
                {/* Next-gen Education gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-emerald-950 to-slate-950">
                  {/* Animated grid pattern */}
                  <div className="absolute inset-0 opacity-20"
                       style={{
                         backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
                         backgroundSize: '40px 40px',
                         animation: 'moveGrid 20s linear infinite'
                       }}>
                  </div>

                  {/* Glowing orbs with modern colors */}
                  <div className="absolute top-1/3 left-1/3 w-40 h-40 rounded-full bg-emerald-500/20 blur-xl animate-pulse"></div>
                  <div className="absolute bottom-1/4 right-1/4 w-32 h-32 rounded-full bg-teal-500/20 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
                  <div className="absolute top-2/3 left-1/2 w-24 h-24 rounded-full bg-green-500/20 blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

                  {/* Additional gradient elements */}
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-emerald-500/5 via-transparent to-transparent"></div>

                  {/* Subtle digital noise texture */}
                  <div className="absolute inset-0 opacity-5 mix-blend-overlay"
                       style={{
                         backgroundImage: 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 200 200\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.65\' numOctaves=\'3\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E")',
                         backgroundSize: '200px 200px'
                       }}>
                  </div>

                  {/* Subtle dot pattern */}
                  <div className="absolute inset-0 opacity-10"
                       style={{
                         backgroundImage: 'radial-gradient(circle, rgba(255, 255, 255, 0.2) 1px, transparent 1px)',
                         backgroundSize: '20px 20px'
                       }}>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-tr from-slate-950/80 to-transparent"></div>

                {/* UI Elements overlay */}
                <div className="absolute inset-0 flex flex-col p-6">
                  {/* Top bar */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-emerald-500"></div>
                        <span className="text-white text-sm font-medium">AgneX Education</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div className="h-8 w-8 rounded-full bg-black/30 backdrop-blur-md flex items-center justify-center border border-white/10">
                        <GraduationCap className="h-4 w-4 text-emerald-400" />
                      </div>
                      <div className="h-8 w-8 rounded-full bg-black/30 backdrop-blur-md flex items-center justify-center border border-white/10">
                        <MessageSquare className="h-4 w-4 text-emerald-400" />
                      </div>
                      <div className="h-8 w-8 rounded-full bg-black/30 backdrop-blur-md flex items-center justify-center border border-white/10">
                        <Bell className="h-4 w-4 text-emerald-400" />
                      </div>
                    </div>
                  </div>

                  {/* Learning Dashboard */}
                  <div className="grid grid-cols-2 gap-4 mb-auto">
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-white text-sm font-medium">Current Courses</h4>
                        <GraduationCap className="h-4 w-4 text-emerald-400" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-white/80">Advanced Mathematics</span>
                          <div className="h-2 w-16 bg-gray-700 rounded-full overflow-hidden">
                            <div className="h-full w-3/4 bg-emerald-500 rounded-full"></div>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-white/80">Computer Science</span>
                          <div className="h-2 w-16 bg-gray-700 rounded-full overflow-hidden">
                            <div className="h-full w-1/2 bg-emerald-500 rounded-full"></div>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-white/80">World History</span>
                          <div className="h-2 w-16 bg-gray-700 rounded-full overflow-hidden">
                            <div className="h-full w-1/4 bg-emerald-500 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-white text-sm font-medium">Learning Stats</h4>
                        <LineChart className="h-4 w-4 text-emerald-400" />
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-black/20 rounded-lg p-2 text-center">
                          <div className="text-xs text-white/60">Study Time</div>
                          <div className="text-sm font-medium text-white">12.5 hrs</div>
                        </div>
                        <div className="bg-black/20 rounded-lg p-2 text-center">
                          <div className="text-xs text-white/60">Assignments</div>
                          <div className="text-sm font-medium text-white">8/10</div>
                        </div>
                        <div className="bg-black/20 rounded-lg p-2 text-center">
                          <div className="text-xs text-white/60">Avg. Score</div>
                          <div className="text-sm font-medium text-white">92%</div>
                        </div>
                        <div className="bg-black/20 rounded-lg p-2 text-center">
                          <div className="text-xs text-white/60">Streak</div>
                          <div className="text-sm font-medium text-white">14 days</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* AI Learning Assistant */}
                  <div className="bg-black/40 backdrop-blur-xl rounded-xl p-4 border border-white/10 mt-4">
                    <div className="flex items-start gap-3">
                      <div className="h-10 w-10 rounded-full bg-emerald-500 flex items-center justify-center flex-shrink-0">
                        <Bot className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-white text-sm font-medium">Learning Assistant</p>
                        <p className="text-xs text-white/80">I noticed you're struggling with calculus derivatives. Would you like me to create a personalized lesson plan?</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              {educationFeatures.slice(0, 3).map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group flex gap-6 p-4 rounded-xl hover:bg-emerald-950/10 transition-colors duration-300"
                >
                  <div className="flex-shrink-0 inline-flex items-center justify-center h-14 w-14 rounded-xl bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border border-white/5 shadow-inner">
                    <div className="text-emerald-500 group-hover:text-emerald-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-emerald-400 transition-colors duration-300">{feature.title}</h3>
                    <p className="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          <div className="grid md:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="space-y-8 order-2 md:order-1"
            >
              {educationFeatures.slice(3).map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group flex gap-6 p-4 rounded-xl hover:bg-emerald-950/10 transition-colors duration-300"
                >
                  <div className="flex-shrink-0 inline-flex items-center justify-center h-14 w-14 rounded-xl bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border border-white/5 shadow-inner">
                    <div className="text-emerald-500 group-hover:text-emerald-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-emerald-400 transition-colors duration-300">{feature.title}</h3>
                    <p className="text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8 }}
              className="relative order-1 md:order-2"
            >
              <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl blur opacity-20"></div>
              <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl border border-white/10 backdrop-blur-sm">
                {/* Next-gen Education Dashboard gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-teal-950 to-slate-950">
                  {/* Animated grid pattern */}
                  <div className="absolute inset-0 opacity-20"
                       style={{
                         backgroundImage: 'linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)',
                         backgroundSize: '40px 40px',
                         animation: 'moveGrid 20s linear infinite'
                       }}>
                  </div>

                  {/* Glowing orbs with modern colors */}
                  <div className="absolute top-1/4 right-1/4 w-40 h-40 rounded-full bg-teal-500/20 blur-xl animate-pulse"></div>
                  <div className="absolute bottom-1/3 left-1/3 w-32 h-32 rounded-full bg-emerald-500/20 blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
                  <div className="absolute top-1/2 right-1/2 w-24 h-24 rounded-full bg-cyan-500/20 blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

                  {/* Additional gradient elements */}
                  <div className="absolute bottom-0 right-0 w-full h-full bg-gradient-radial from-teal-500/5 via-transparent to-transparent"></div>

                  {/* Subtle digital noise texture */}
                  <div className="absolute inset-0 opacity-5 mix-blend-overlay"
                       style={{
                         backgroundImage: 'url("data:image/svg+xml,%3Csvg viewBox=\'0 0 200 200\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.65\' numOctaves=\'3\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E")',
                         backgroundSize: '200px 200px'
                       }}>
                  </div>

                  {/* Subtle hexagon pattern */}
                  <div className="absolute inset-0 opacity-10"
                       style={{
                         backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'28\' height=\'49\' viewBox=\'0 0 28 49\'%3E%3Cg fill-rule=\'evenodd\'%3E%3Cg id=\'hexagons\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'nonzero\'%3E%3Cpath d=\'M13.99 9.25l13 7.5v15l-13 7.5L1 31.75v-15l12.99-7.5zM3 17.9v12.7l10.99 6.34 11-6.35V17.9l-11-6.34L3 17.9z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                         backgroundSize: '30px 52px'
                       }}>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent to-slate-950/80"></div>

                {/* Interactive Learning Interface */}
                <div className="absolute inset-0 flex flex-col p-6">
                  {/* Top bar */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                      <div className="flex items-center gap-2">
                        <Bot className="h-4 w-4 text-emerald-400" />
                        <span className="text-white text-sm font-medium">Interactive Learning</span>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-full px-4 py-2 border border-white/10">
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span className="text-white text-sm font-medium">Level 4</span>
                      </div>
                    </div>
                  </div>

                  {/* Interactive content */}
                  <div className="bg-black/30 backdrop-blur-md rounded-xl p-4 border border-white/10 mb-4 flex-1">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-white text-sm font-medium">Physics: Understanding Gravity</h4>
                      <div className="flex items-center gap-2">
                        <div className="px-2 py-1 rounded-md bg-emerald-500/20 text-xs text-emerald-200">Interactive</div>
                      </div>
                    </div>

                    <div className="relative h-48 bg-black/20 rounded-lg mb-4 overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-emerald-500/30 animate-pulse"></div>
                        <div className="absolute w-4 h-4 rounded-full bg-white animate-bounce" style={{ animationDuration: '2s' }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <p className="text-xs text-white/80">Adjust the parameters to see how gravity affects the object:</p>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-white/80 w-20">Mass:</span>
                        <div className="h-2 flex-1 bg-gray-700 rounded-full overflow-hidden">
                          <div className="h-full w-2/3 bg-emerald-500 rounded-full"></div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-white/80 w-20">Distance:</span>
                        <div className="h-2 flex-1 bg-gray-700 rounded-full overflow-hidden">
                          <div className="h-full w-1/2 bg-emerald-500 rounded-full"></div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-white/80 w-20">Gravity:</span>
                        <div className="h-2 flex-1 bg-gray-700 rounded-full overflow-hidden">
                          <div className="h-full w-1/4 bg-emerald-500 rounded-full"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Progress and gamification */}
                  <div className="grid grid-cols-4 gap-3">
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-3 border border-white/10">
                      <div className="flex flex-col items-center">
                        <Sparkles className="h-4 w-4 text-yellow-400 mb-1" />
                        <div className="text-xs text-white/80">Points</div>
                        <div className="text-sm font-bold text-white">1,250</div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-3 border border-white/10">
                      <div className="flex flex-col items-center">
                        <Trophy className="h-4 w-4 text-yellow-400 mb-1" />
                        <div className="text-xs text-white/80">Badges</div>
                        <div className="text-sm font-bold text-white">8</div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-3 border border-white/10">
                      <div className="flex flex-col items-center">
                        <Calendar className="h-4 w-4 text-emerald-400 mb-1" />
                        <div className="text-xs text-white/80">Streak</div>
                        <div className="text-sm font-bold text-white">14 days</div>
                      </div>
                    </div>
                    <div className="bg-black/30 backdrop-blur-md rounded-xl p-3 border border-white/10">
                      <div className="flex flex-col items-center">
                        <Users className="h-4 w-4 text-blue-400 mb-1" />
                        <div className="text-xs text-white/80">Friends</div>
                        <div className="text-sm font-bold text-white">24</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="relative py-24 overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background/90 to-background"></div>
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[1200px] h-[1200px] rounded-full">
            <div className="absolute inset-0 bg-gradient-conic from-blue-500/20 via-purple-500/20 to-blue-500/20 blur-3xl opacity-30 animate-slow-spin"></div>
          </div>
        </div>

        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <div className="relative rounded-3xl overflow-hidden">
              {/* Glassmorphism card */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-xl border border-white/10"></div>

              {/* Content */}
              <div className="relative p-8 md:p-12 lg:p-16 text-center">
                <Badge className="mb-6" variant="outline">
                  <span className="flex items-center gap-2">
                    <Sparkles className="h-3.5 w-3.5" />
                    Join the Waitlist
                  </span>
                </Badge>

                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500">
                  Be Among the First to Experience AgneX OS
                </h2>

                <p className="text-lg md:text-xl text-foreground/80 max-w-2xl mx-auto mb-10">
                  Sign up to receive updates on our development progress and get early access to the beta version
                  when it becomes available.
                </p>

                <div className="flex flex-wrap justify-center gap-6">
                  <Button size="lg" className="rounded-full px-8 py-6 text-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/30 transition-all duration-300">
                    <span className="flex items-center gap-2">
                      <Briefcase className="h-5 w-5" />
                      Enterprise Waitlist
                    </span>
                  </Button>

                  <Button size="lg" variant="outline" className="rounded-full px-8 py-6 text-lg backdrop-blur-sm bg-white/5 border-white/10 hover:bg-white/10 shadow-lg transition-all duration-300">
                    <span className="flex items-center gap-2">
                      <GraduationCap className="h-5 w-5" />
                      Education Waitlist
                    </span>
                  </Button>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-12 -right-12 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-purple-500/10 rounded-full blur-3xl"></div>
              </div>
            </div>

            {/* Floating badges */}
            <div className="absolute -top-5 left-10 bg-background/80 backdrop-blur-md rounded-full px-4 py-2 shadow-lg border border-blue-500/20">
              <div className="flex items-center gap-2">
                <Command className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Early Access</span>
              </div>
            </div>
            <div className="absolute -bottom-5 right-10 bg-background/80 backdrop-blur-md rounded-full px-4 py-2 shadow-lg border border-purple-500/20">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-500" />
                <span className="font-medium">Limited Spots</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Add custom animations */}
      <style jsx global>{`
        @keyframes slow-spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .animate-slow-spin {
          animation: slow-spin 20s linear infinite;
        }

        @keyframes moveGrid {
          0% {
            background-position: 0 0;
          }
          100% {
            background-position: 40px 40px;
          }
        }
      `}</style>
    </div>
  )
}
